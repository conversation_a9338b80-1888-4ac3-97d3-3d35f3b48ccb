"""
Text-to-Speech service implementation using edge-tts.
"""

import io
import uuid
import base64
from typing import Any, Dict, List, Optional

import edge_tts
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)


def _map_speed_to_rate(speed: float) -> str:
    """Map speed (0.5-2.0) to edge-tts rate string like "+20%" or "-30%"."""
    clamped = max(0.5, min(2.0, speed))
    percent = int(round((clamped - 1.0) * 100))
    sign = "+" if percent >= 0 else ""
    return f"{sign}{percent}%"


def _map_pitch_to_edge(pitch: float) -> str:
    """Map pitch (0.5-2.0) to edge-tts pitch string in Hz, e.g., "+0Hz", "+50Hz", "-50Hz"."""
    clamped = max(0.5, min(2.0, pitch))
    # Map 1.0 -> 0Hz, 0.5 -> -50Hz, 2.0 -> +50Hz (linear approx)
    hz = int(round((clamped - 1.0) * 100)) // 2
    sign = "+" if hz >= 0 else ""
    return f"{sign}{hz}Hz"


class TTSService:
    """Text-to-Speech service backed by Microsoft Edge online TTS via edge-tts."""

    def __init__(self):
        pass

    @staticmethod
    def _edge_stream_format(output_format: Optional[str]) -> str:
        """Map simple output format to edge-tts stream format string."""
        fmt = (output_format or "mp3").lower()
        if fmt in ("mp3", "audio/mpeg"):
            return "audio-24khz-48kbitrate-mono-mp3"
        if fmt in ("wav", "audio/wav", "pcm"):
            return "riff-24khz-16bit-mono-pcm"
        if fmt in ("webm", "opus", "audio/webm"):
            return "webm-24khz-16bit-mono-opus"
        # default fallback
        return "audio-24khz-48kbitrate-mono-mp3"

    async def synthesize(
        self,
        text: str,
        voice: str = "en-US-AriaNeural",
        speed: float = 1.0,
        pitch: float = 1.0,
        output_format: str = "mp3",
    ) -> Dict[str, Any]:
        """
        Convert text to speech and return metadata. Use synthesize_stream to get bytes.
        """
        audio_id = str(uuid.uuid4())
        audio_filename = f"{audio_id}.{output_format}"
        audio_url = f"/api/v1/tts/audio/{audio_filename}"

        word_count = len(text.split())
        duration = (word_count / 150) * 60 / max(0.5, min(2.0, speed))

        return {
            "audio_url": audio_url,
            "audio_filename": audio_filename,
            "duration": duration,
        }

    async def synthesize_stream(
        self,
        text: str,
        voice: str = "en-US-AriaNeural",
        speed: float = 1.0,
        pitch: float = 1.0,
        output_format: str = "mp3",
    ) -> bytes:
        """
        Convert text to speech and return audio data as bytes using edge-tts.
        """
        rate = _map_speed_to_rate(speed)
        pitch_s = _map_pitch_to_edge(pitch)

        communicate = edge_tts.Communicate(
            text=text,
            voice=voice,
            rate=rate,
            pitch=pitch_s,
        )

        # Note: Some edge-tts versions do not accept a 'format' parameter in stream().
        # Use default stream format.
        buf = io.BytesIO()
        async for chunk in communicate.stream():
            if chunk.get("type") == "audio":
                buf.write(chunk.get("data", b""))
        return buf.getvalue()

    async def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get list of available voices from edge-tts."""
        voices = await edge_tts.list_voices()
        results: List[Dict[str, Any]] = []
        for v in voices:
            results.append(
                {
                    "id": v.get("Name") or v.get("ShortName") or "",
                    "name": v.get("FriendlyName") or v.get("ShortName") or v.get("Name") or "",
                    "language": v.get("Locale") or v.get("LocaleName") or "",
                    "gender": (v.get("Gender") or "").lower(),
                    "sample_rate": 24000,
                    "provider": "edge-tts",
                }
            )
        return results

    async def get_voices_by_language(self, language: str) -> List[Dict[str, Any]]:
        all_voices = await self.get_available_voices()
        return [voice for voice in all_voices if voice["language"] == language]

    async def synthesize_with_timings(
        self,
        text: str,
        voice: str = "en-US-AriaNeural",
        speed: float = 1.0,
        pitch: float = 1.0,
        output_format: str = "mp3",
    ) -> Dict[str, Any]:
        """
        Convert text to speech and return base64 audio with perfect word boundary timings.
        Uses sentence-by-sentence synthesis for maximum precision, eliminating cumulative errors.
        """
        rate = _map_speed_to_rate(speed)
        pitch_s = _map_pitch_to_edge(pitch)

        # Use per-word synthesis for maximum precision
        timings = await self._synthesize_per_word_timings(text, voice, rate, pitch_s)

        # Generate final audio for the complete text
        communicate = edge_tts.Communicate(
            text=text,
            voice=voice,
            rate=rate,
            pitch=pitch_s,
        )

        audio_bytes = io.BytesIO()
        async for chunk in communicate.stream():
            if chunk.get("type") == "audio":
                audio_bytes.write(chunk.get("data", b""))

        audio_b64 = base64.b64encode(audio_bytes.getvalue()).decode("ascii")
        mime = "audio/mpeg" if output_format == "mp3" else "audio/wav"

        return {
            "audio_base64": audio_b64,
            "mime": mime,
            "timings": timings,
        }

    def _generate_word_timings(
        self,
        text: str,
        sentence_boundaries: List[Dict[str, Any]],
        speed: float
    ) -> List[Dict[str, Any]]:
        """
        Generate word-level timings based on sentence boundaries and text analysis.
        Ensures smooth, monotonic timing progression.
        """
        import re

        # Split text into words with their positions
        words_with_positions = []
        word_pattern = r'\S+'
        for match in re.finditer(word_pattern, text):
            words_with_positions.append({
                'word': match.group(),
                'start_pos': match.start(),
                'end_pos': match.end(),
                'length': len(match.group())
            })

        if not words_with_positions:
            return []

        # If no sentence boundaries, estimate based on total duration
        if not sentence_boundaries:
            # Estimate total duration based on word count and speed
            total_words = len(words_with_positions)
            # Average speaking rate: ~150 words per minute, adjusted by speed
            estimated_duration_ms = (total_words / (150 * speed)) * 60 * 1000

            timings = []
            for i, word_info in enumerate(words_with_positions):
                # Use smoother distribution with slight randomization to avoid exact uniformity
                base_time = (i / max(1, total_words - 1)) * estimated_duration_ms
                # Add small variation based on word length (longer words take slightly more time)
                word_factor = 1.0 + (word_info['length'] - 5) * 0.02  # Adjust timing by word length
                time_ms = int(base_time * word_factor)

                timings.append({
                    "time_ms": max(0, time_ms),
                    "text_offset": word_info['start_pos'],
                    "text_length": word_info['length'],
                    "word": word_info['word']
                })

            # Ensure monotonic progression
            for i in range(1, len(timings)):
                if timings[i]["time_ms"] <= timings[i-1]["time_ms"]:
                    timings[i]["time_ms"] = timings[i-1]["time_ms"] + 50  # Minimum 50ms gap

            return timings

        # Use sentence boundaries to estimate word timings
        timings = []
        sentence_boundaries.sort(key=lambda x: x["time_ms"])

        # Find sentences in text
        sentence_pattern = r'[.!?]+\s*'
        sentences = re.split(sentence_pattern, text)
        sentences = [s.strip() for s in sentences if s.strip()]

        current_text_pos = 0

        for i, sentence in enumerate(sentences):
            if not sentence:
                continue

            # Find sentence start position in original text
            sentence_start = text.find(sentence, current_text_pos)
            if sentence_start == -1:
                continue

            sentence_end = sentence_start + len(sentence)
            current_text_pos = sentence_end

            # Get timing for this sentence
            sentence_start_time = 0
            sentence_duration = 0

            if i < len(sentence_boundaries):
                sentence_start_time = sentence_boundaries[i]["time_ms"]
                if i + 1 < len(sentence_boundaries):
                    sentence_duration = sentence_boundaries[i + 1]["time_ms"] - sentence_start_time
                else:
                    # Last sentence, estimate duration based on word count
                    sentence_words_count = len(sentence.split())
                    sentence_duration = int((sentence_words_count / (150 * speed)) * 60 * 1000)

            # Find words in this sentence
            sentence_words = []
            for word_info in words_with_positions:
                if sentence_start <= word_info['start_pos'] < sentence_end:
                    sentence_words.append(word_info)

            if not sentence_words:
                continue

            # Distribute words within sentence duration with smoother timing
            for j, word_info in enumerate(sentence_words):
                if sentence_duration > 0 and len(sentence_words) > 1:
                    # Use smoother distribution curve instead of linear
                    word_progress = j / (len(sentence_words) - 1)
                    # Apply easing function for more natural timing
                    eased_progress = word_progress * word_progress * (3.0 - 2.0 * word_progress)  # Smoothstep

                    # Add word length factor
                    word_factor = 1.0 + (word_info['length'] - 5) * 0.01
                    base_time = sentence_start_time + int(eased_progress * sentence_duration)
                    word_time = int(base_time * word_factor)
                else:
                    word_time = sentence_start_time

                timings.append({
                    "time_ms": word_time,
                    "text_offset": word_info['start_pos'],
                    "text_length": word_info['length'],
                    "word": word_info['word']
                })

        # Sort by text position first, then ensure monotonic time progression
        timings.sort(key=lambda x: x["text_offset"])

        # Ensure monotonic time progression with minimum gaps
        min_gap_ms = 80  # Minimum 80ms between words for smooth highlighting
        for i in range(1, len(timings)):
            if timings[i]["time_ms"] <= timings[i-1]["time_ms"]:
                timings[i]["time_ms"] = timings[i-1]["time_ms"] + min_gap_ms

        # Final cleanup - ensure no negative times
        for timing in timings:
            timing["time_ms"] = max(0, timing["time_ms"])

        return timings

    async def _synthesize_per_word_timings(
        self,
        text: str,
        voice: str,
        rate: str,
        pitch: str
    ) -> List[Dict[str, Any]]:
        """
        Generate precise word timings by analyzing sentence boundaries and word distribution.
        Uses a hybrid approach combining sentence-level timing with intelligent word distribution.
        """
        import re

        # Tokenize text into words with positions
        words_with_positions = []
        word_pattern = r'\S+'
        for match in re.finditer(word_pattern, text):
            words_with_positions.append({
                'word': match.group(),
                'start_pos': match.start(),
                'end_pos': match.end(),
                'length': len(match.group())
            })

        if not words_with_positions:
            return []

        # Get sentence boundaries from edge-tts
        communicate = edge_tts.Communicate(
            text=text,
            voice=voice,
            rate=rate,
            pitch=pitch,
        )

        sentence_boundaries = []
        async for chunk in communicate.stream():
            chunk_type = (chunk.get("type") or "").lower()
            if chunk_type in ("sentenceboundary", "sentence_boundary", "sentence-boundary"):
                offset = int(chunk.get("offset", 0))
                time_ms = offset // 10_000
                sentence_boundaries.append({
                    "time_ms": time_ms,
                    "text": chunk.get("text", ""),
                })

        # Generate precise word timings using advanced distribution
        return self._generate_precise_word_timings(text, words_with_positions, sentence_boundaries)

    def _generate_precise_word_timings(
        self,
        text: str,
        words_with_positions: List[Dict[str, Any]],
        sentence_boundaries: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Generate precise word timings using sentence boundaries and linguistic analysis.
        """
        import re

        timings = []

        if not sentence_boundaries:
            # Fallback to uniform distribution
            return self._generate_uniform_timings(words_with_positions)

        # Sort sentence boundaries by time
        sentence_boundaries.sort(key=lambda x: x["time_ms"])

        # Split text into sentences
        sentence_pattern = r'[.!?]+\s*'
        sentences = re.split(sentence_pattern, text)
        sentences = [s.strip() for s in sentences if s.strip()]

        current_text_pos = 0

        for i, sentence in enumerate(sentences):
            if not sentence:
                continue

            # Find sentence boundaries
            sentence_start = text.find(sentence, current_text_pos)
            if sentence_start == -1:
                continue

            sentence_end = sentence_start + len(sentence)
            current_text_pos = sentence_end

            # Get timing for this sentence
            sentence_start_time = 0
            sentence_end_time = 5000  # Default 5 seconds

            if i < len(sentence_boundaries):
                sentence_start_time = sentence_boundaries[i]["time_ms"]
                if i + 1 < len(sentence_boundaries):
                    sentence_end_time = sentence_boundaries[i + 1]["time_ms"]
                else:
                    # Estimate end time for last sentence
                    sentence_words = len(sentence.split())
                    estimated_duration = sentence_words * 400  # 400ms per word average
                    sentence_end_time = sentence_start_time + estimated_duration

            # Find words in this sentence
            sentence_words = []
            for word_info in words_with_positions:
                if sentence_start <= word_info['start_pos'] < sentence_end:
                    sentence_words.append(word_info)

            if not sentence_words:
                continue

            # Distribute words with linguistic timing
            sentence_duration = sentence_end_time - sentence_start_time
            self._distribute_words_linguistically(
                sentence_words, sentence_start_time, sentence_duration, timings
            )

        # Sort by text position and ensure monotonic progression
        timings.sort(key=lambda x: x["text_offset"])
        self._ensure_monotonic_timing(timings, min_gap_ms=30)

        return timings

    def _distribute_words_linguistically(
        self,
        words: List[Dict[str, Any]],
        start_time: int,
        duration: int,
        timings: List[Dict[str, Any]]
    ):
        """
        Distribute words within a sentence using linguistic timing patterns.
        """
        if not words:
            return

        if len(words) == 1:
            # Single word gets the start time
            word = words[0]
            timings.append({
                "time_ms": start_time,
                "text_offset": word['start_pos'],
                "text_length": word['length'],
                "word": word['word']
            })
            return

        # Calculate timing weights based on word characteristics
        total_weight = 0
        word_weights = []

        for word in words:
            # Base weight
            weight = 1.0

            # Longer words get slightly more time
            if word['length'] > 6:
                weight *= 1.2
            elif word['length'] < 3:
                weight *= 0.8

            # Function words (articles, prepositions) get less time
            function_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
            if word['word'].lower() in function_words:
                weight *= 0.7

            # Punctuation gets minimal time
            if word['word'] in '.,!?;:':
                weight *= 0.3

            word_weights.append(weight)
            total_weight += weight

        # Distribute time based on weights
        current_time = start_time

        for i, (word, weight) in enumerate(zip(words, word_weights)):
            if i == len(words) - 1:
                # Last word gets remaining time
                word_time = current_time
            else:
                # Calculate time based on weight
                time_portion = (weight / total_weight) * duration
                word_time = current_time
                current_time += int(time_portion)

            timings.append({
                "time_ms": word_time,
                "text_offset": word['start_pos'],
                "text_length": word['length'],
                "word": word['word']
            })

    def _generate_uniform_timings(self, words_with_positions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Generate uniform timing distribution as fallback.
        """
        timings = []
        word_count = len(words_with_positions)

        # Estimate total duration: ~150 words per minute
        total_duration_ms = (word_count / 150) * 60 * 1000

        for i, word_info in enumerate(words_with_positions):
            if word_count == 1:
                time_ms = 0
            else:
                progress = i / (word_count - 1)
                time_ms = int(progress * total_duration_ms)

            timings.append({
                "time_ms": time_ms,
                "text_offset": word_info['start_pos'],
                "text_length": word_info['length'],
                "word": word_info['word']
            })

        return timings

    def _generate_ssml_with_bookmarks(
        self,
        text: str,
        voice: str,
        rate: str,
        pitch: str
    ) -> tuple[str, Dict[str, Dict[str, Any]]]:
        """
        Generate SSML with bookmarks for each word/token to get precise timing.
        Returns SSML string and a mapping of bookmark names to token info.
        """

        # Tokenize text into words (for English) or characters (for CJK)
        tokens = self._tokenize_text(text)

        # Limit tokens to avoid SSML being too long (max 200 bookmarks per segment)
        if len(tokens) > 200:
            tokens = tokens[:200]
            logger.warning(f"Text truncated to 200 tokens to avoid SSML length limits")

        # Build token map for bookmark -> token info mapping
        token_map = {}
        current_pos = 0

        # Build SSML with bookmarks
        ssml_parts = [
            f'<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="en-US">',
            f'<voice name="{voice}">',
            f'<prosody rate="{rate}" pitch="{pitch}">'
        ]

        for i, token in enumerate(tokens):
            bookmark_name = f"word_{i}"

            # Find token position in original text
            token_start = text.find(token, current_pos)
            if token_start == -1:
                token_start = current_pos

            token_map[bookmark_name] = {
                "text_offset": token_start,
                "text_length": len(token),
                "word": token,
                "index": i
            }

            # Add bookmark before the token
            ssml_parts.append(f'<bookmark mark="{bookmark_name}"/>{self._escape_ssml(token)}')
            current_pos = token_start + len(token)

        ssml_parts.extend([
            '</prosody>',
            '</voice>',
            '</speak>'
        ])

        ssml_text = ''.join(ssml_parts)
        return ssml_text, token_map

    def _tokenize_text(self, text: str) -> List[str]:
        """
        Tokenize text into words (English) or characters (CJK).
        """
        import re

        # Check if text contains CJK characters
        cjk_pattern = r'[\u4e00-\u9fff\u3040-\u309f\u30a0-\u30ff\uac00-\ud7af]'
        has_cjk = bool(re.search(cjk_pattern, text))

        if has_cjk:
            # For CJK text, tokenize by characters (excluding spaces and punctuation)
            tokens = []
            for char in text:
                if char.strip() and not re.match(r'[^\w\s]', char):
                    tokens.append(char)
                elif char in '，。！？,.!?;:':
                    # Include punctuation as separate tokens
                    tokens.append(char)
            return tokens
        else:
            # For English text, tokenize by words
            word_pattern = r'\S+'
            tokens = re.findall(word_pattern, text)
            return tokens

    def _escape_ssml(self, text: str) -> str:
        """
        Escape special characters for SSML.
        """
        return (text
                .replace('&', '&amp;')
                .replace('<', '&lt;')
                .replace('>', '&gt;')
                .replace('"', '&quot;')
                .replace("'", '&apos;'))

    def _map_bookmarks_to_timings(
        self,
        bookmark_events: List[Dict[str, Any]],
        token_map: Dict[str, Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Map bookmark events to word timings.
        """
        timings = []

        # Sort bookmark events by time
        bookmark_events.sort(key=lambda x: x["time_ms"])

        for event in bookmark_events:
            bookmark_name = event["bookmark_name"]
            if bookmark_name in token_map:
                token_info = token_map[bookmark_name]
                timings.append({
                    "time_ms": event["time_ms"],
                    "text_offset": token_info["text_offset"],
                    "text_length": token_info["text_length"],
                    "word": token_info["word"]
                })

        # Sort by text offset to maintain original order
        timings.sort(key=lambda x: x["text_offset"])

        # Ensure monotonic time progression
        for i in range(1, len(timings)):
            if timings[i]["time_ms"] <= timings[i-1]["time_ms"]:
                timings[i]["time_ms"] = timings[i-1]["time_ms"] + 50  # Minimum 50ms gap

        return timings

    def _estimate_audio_duration(self, audio_data: bytes, output_format: str) -> int:
        """
        Estimate audio duration in milliseconds from audio data.
        This is a rough estimation based on file size and format.
        """
        if not audio_data:
            return 0

        # Rough estimation based on typical bitrates
        # MP3 at 48kbps: ~6KB per second
        # This is approximate but better than pure word-count estimation
        if output_format.lower() == "mp3":
            # Assume 48kbps bitrate (6KB/s)
            estimated_seconds = len(audio_data) / (6 * 1024)
        else:
            # For WAV, assume 16-bit 24kHz mono: ~48KB/s
            estimated_seconds = len(audio_data) / (48 * 1024)

        return int(estimated_seconds * 1000)

    def _generate_word_timings_v2(
        self,
        text: str,
        sentence_boundaries: List[Dict[str, Any]],
        speed: float,
        actual_duration_ms: int
    ) -> List[Dict[str, Any]]:
        """
        Generate word-level timings with improved accuracy using actual audio duration.
        """
        import re

        # Split text into words with their positions
        words_with_positions = []
        word_pattern = r'\S+'
        for match in re.finditer(word_pattern, text):
            words_with_positions.append({
                'word': match.group(),
                'start_pos': match.start(),
                'end_pos': match.end(),
                'length': len(match.group())
            })

        if not words_with_positions:
            return []

        # Use actual duration if available, otherwise estimate
        total_duration_ms = actual_duration_ms if actual_duration_ms > 0 else self._estimate_duration_from_text(text, speed)

        # If we have sentence boundaries, use them for more accurate timing
        if sentence_boundaries and len(sentence_boundaries) > 0:
            return self._distribute_words_by_sentences(
                text, words_with_positions, sentence_boundaries, total_duration_ms
            )
        else:
            # Fallback to uniform distribution
            return self._distribute_words_uniformly(
                words_with_positions, total_duration_ms
            )

    def _estimate_duration_from_text(self, text: str, speed: float) -> int:
        """
        Estimate duration based on text characteristics and speed.
        """
        word_count = len(text.split())

        # Base estimation: ~150 words per minute for normal speech
        # Adjust for punctuation and sentence complexity
        punctuation_count = len([c for c in text if c in '.,!?;:'])
        complexity_factor = 1.0 + (punctuation_count / max(1, word_count)) * 0.2

        base_duration = (word_count / (150 * speed)) * 60 * 1000
        return int(base_duration * complexity_factor)

    def _distribute_words_by_sentences(
        self,
        text: str,
        words_with_positions: List[Dict[str, Any]],
        sentence_boundaries: List[Dict[str, Any]],
        total_duration_ms: int
    ) -> List[Dict[str, Any]]:
        """
        Distribute words based on sentence boundaries with improved accuracy.
        """
        import re

        timings = []
        sentence_boundaries.sort(key=lambda x: x["time_ms"])

        # Find sentences in text
        sentence_pattern = r'[.!?]+\s*'
        sentences = re.split(sentence_pattern, text)
        sentences = [s.strip() for s in sentences if s.strip()]

        current_text_pos = 0

        for i, sentence in enumerate(sentences):
            if not sentence:
                continue

            # Find sentence start position in original text
            sentence_start = text.find(sentence, current_text_pos)
            if sentence_start == -1:
                continue

            sentence_end = sentence_start + len(sentence)
            current_text_pos = sentence_end

            # Get timing for this sentence
            sentence_start_time = 0
            sentence_end_time = total_duration_ms

            if i < len(sentence_boundaries):
                sentence_start_time = sentence_boundaries[i]["time_ms"]
                if i + 1 < len(sentence_boundaries):
                    sentence_end_time = sentence_boundaries[i + 1]["time_ms"]

            sentence_duration = sentence_end_time - sentence_start_time

            # Find words in this sentence
            sentence_words = []
            for word_info in words_with_positions:
                if sentence_start <= word_info['start_pos'] < sentence_end:
                    sentence_words.append(word_info)

            if not sentence_words:
                continue

            # Distribute words within sentence with more natural timing
            for j, word_info in enumerate(sentence_words):
                if len(sentence_words) == 1:
                    word_time = sentence_start_time
                else:
                    # Use more natural distribution - words at beginning and end get more time
                    progress = j / (len(sentence_words) - 1)
                    # Apply slight easing for more natural speech rhythm
                    eased_progress = progress * (2 - progress)  # Ease-out
                    word_time = sentence_start_time + int(eased_progress * sentence_duration)

                timings.append({
                    "time_ms": max(0, word_time),
                    "text_offset": word_info['start_pos'],
                    "text_length": word_info['length'],
                    "word": word_info['word']
                })

        # Ensure monotonic progression and reasonable gaps
        timings.sort(key=lambda x: x["text_offset"])
        self._ensure_monotonic_timing(timings, min_gap_ms=50)

        # Apply aggressive timing adjustment for better sync
        self._adjust_timing_for_sync(timings)

        return timings

    def _distribute_words_uniformly(
        self,
        words_with_positions: List[Dict[str, Any]],
        total_duration_ms: int
    ) -> List[Dict[str, Any]]:
        """
        Distribute words uniformly across the total duration.
        """
        timings = []
        word_count = len(words_with_positions)

        for i, word_info in enumerate(words_with_positions):
            if word_count == 1:
                word_time = 0
            else:
                progress = i / (word_count - 1)
                word_time = int(progress * total_duration_ms)

            timings.append({
                "time_ms": max(0, word_time),
                "text_offset": word_info['start_pos'],
                "text_length": word_info['length'],
                "word": word_info['word']
            })

        self._ensure_monotonic_timing(timings, min_gap_ms=60)
        return timings

    def _ensure_monotonic_timing(self, timings: List[Dict[str, Any]], min_gap_ms: int = 50):
        """
        Ensure timing progression is monotonic with minimum gaps.
        """
        for i in range(1, len(timings)):
            if timings[i]["time_ms"] <= timings[i-1]["time_ms"]:
                timings[i]["time_ms"] = timings[i-1]["time_ms"] + min_gap_ms

    def _adjust_timing_for_sync(self, timings: List[Dict[str, Any]]):
        """
        Apply aggressive timing adjustments for better synchronization.
        Move all timings earlier and ensure smooth progression from the start.
        """
        if not timings:
            return

        # Apply a base offset to all timings to compensate for audio processing delay
        base_offset_ms = 80  # Reduced offset for better balance

        for timing in timings:
            # Apply base offset
            timing["time_ms"] = max(0, timing["time_ms"] - base_offset_ms)

        # Optimize first few words for smooth start
        if len(timings) >= 1:
            timings[0]["time_ms"] = 0  # First word starts immediately

        if len(timings) >= 2:
            # Second word should come quickly to avoid long pause
            timings[1]["time_ms"] = min(300, timings[1]["time_ms"])

        if len(timings) >= 3:
            # Third word continues the smooth progression
            timings[2]["time_ms"] = min(timings[1]["time_ms"] + 250, timings[2]["time_ms"])

        if len(timings) >= 4:
            # Fourth word maintains reasonable pace
            timings[3]["time_ms"] = min(timings[2]["time_ms"] + 300, timings[3]["time_ms"])

        # Re-ensure monotonic progression with optimized gaps
        for i in range(1, len(timings)):
            if timings[i]["time_ms"] <= timings[i-1]["time_ms"]:
                # Use variable gaps: smaller for early words, normal for later words
                gap = 150 if i <= 3 else 80
                timings[i]["time_ms"] = timings[i-1]["time_ms"] + gap
