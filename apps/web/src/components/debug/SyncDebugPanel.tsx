import React, { useState, useEffect } from 'react'
import { syncTester, quickSyncTest, optimizeSync, SyncTestResult } from '../../utils/syncTester'
import { enhancedTTSService } from '../../services/speech/EnhancedTTSService'

interface SyncDebugPanelProps {
  isOpen: boolean
  onClose: () => void
}

export const SyncDebugPanel: React.FC<SyncDebugPanelProps> = ({ isOpen, onClose }) => {
  const [syncStatus, setSyncStatus] = useState<any>(null)
  const [testResult, setTestResult] = useState<SyncTestResult | null>(null)
  const [isTestingActive, setIsTestingActive] = useState(false)
  const [realTimeStatus, setRealTimeStatus] = useState<any>(null)
  const [customOffset, setCustomOffset] = useState(-30)
  const [testText, setTestText] = useState("The quick brown fox jumps over the lazy dog. This is a perfect synchronization test.")

  useEffect(() => {
    if (isOpen) {
      updateSyncStatus()
      const interval = setInterval(updateSyncStatus, 1000)
      return () => clearInterval(interval)
    }
  }, [isOpen])

  const updateSyncStatus = () => {
    const status = enhancedTTSService.getSyncStatus()
    setSyncStatus(status)
  }

  const runQuickTest = async () => {
    setIsTestingActive(true)
    try {
      const result = await quickSyncTest(testText)
      setTestResult(result)
    } catch (error) {
      console.error('Sync test failed:', error)
    } finally {
      setIsTestingActive(false)
    }
  }

  const runAutoCalibration = async () => {
    setIsTestingActive(true)
    try {
      const result = await syncTester.autoCalibrate(testText)
      console.log('Auto calibration completed:', result)
      updateSyncStatus()
      // 运行一次测试查看效果
      const testResult = await quickSyncTest(testText)
      setTestResult(testResult)
    } catch (error) {
      console.error('Auto calibration failed:', error)
    } finally {
      setIsTestingActive(false)
    }
  }

  const applyCustomOffset = () => {
    enhancedTTSService.configurePerfectSync({ baseOffset: customOffset })
    updateSyncStatus()
  }

  const resetCalibration = () => {
    enhancedTTSService.resetSyncCalibration()
    updateSyncStatus()
  }

  const startRealTimeMonitoring = () => {
    const stopMonitoring = syncTester.startRealTimeMonitoring((status) => {
      setRealTimeStatus(status)
    })

    // 开始朗读测试文本
    enhancedTTSService.speak(testText)

    // 10秒后停止监控
    setTimeout(() => {
      stopMonitoring()
      setRealTimeStatus(null)
    }, 10000)
  }

  const getSyncQualityColor = (accuracy: number) => {
    if (accuracy > 95) return 'text-green-600'
    if (accuracy > 85) return 'text-blue-600'
    if (accuracy > 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getSyncQualityBadge = (quality: string) => {
    const colors = {
      excellent: 'bg-green-100 text-green-800',
      good: 'bg-blue-100 text-blue-800',
      fair: 'bg-yellow-100 text-yellow-800',
      poor: 'bg-red-100 text-red-800'
    }
    return colors[quality as keyof typeof colors] || colors.poor
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto m-4">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">TTS同步调试面板</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>

          {/* 当前同步状态 */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-semibold mb-3">当前同步状态</h3>
            {syncStatus && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-gray-600">基础偏移量:</span>
                  <span className="ml-2 font-mono">{syncStatus.baseOffset}ms</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600">自适应偏移量:</span>
                  <span className="ml-2 font-mono">{syncStatus.adaptiveOffset.toFixed(2)}ms</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600">平均误差:</span>
                  <span className="ml-2 font-mono">{syncStatus.avgError.toFixed(2)}ms</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600">校准历史:</span>
                  <span className="ml-2 font-mono">{syncStatus.calibrationHistory.length} 次</span>
                </div>
              </div>
            )}
          </div>

          {/* 测试文本配置 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3">测试文本</h3>
            <textarea
              value={testText}
              onChange={(e) => setTestText(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg resize-none"
              rows={3}
              placeholder="输入用于测试的文本..."
            />
          </div>

          {/* 控制按钮 */}
          <div className="mb-6 flex flex-wrap gap-3">
            <button
              onClick={runQuickTest}
              disabled={isTestingActive}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {isTestingActive ? '测试中...' : '快速测试'}
            </button>
            <button
              onClick={runAutoCalibration}
              disabled={isTestingActive}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
            >
              {isTestingActive ? '校准中...' : '自动校准'}
            </button>
            <button
              onClick={startRealTimeMonitoring}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
            >
              实时监控
            </button>
            <button
              onClick={resetCalibration}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              重置校准
            </button>
          </div>

          {/* 手动偏移调整 */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-semibold mb-3">手动调整偏移量</h3>
            <div className="flex items-center gap-3">
              <input
                type="range"
                min="-200"
                max="200"
                step="10"
                value={customOffset}
                onChange={(e) => setCustomOffset(Number(e.target.value))}
                className="flex-1"
              />
              <span className="font-mono w-16 text-center">{customOffset}ms</span>
              <button
                onClick={applyCustomOffset}
                className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                应用
              </button>
            </div>
            <div className="mt-2 text-sm text-gray-600">
              负值表示提前高亮，正值表示延迟高亮
            </div>
          </div>

          {/* 测试结果 */}
          {testResult && (
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-semibold mb-3">测试结果</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-gray-600">同步准确度:</span>
                  <span className={`ml-2 font-bold ${getSyncQualityColor(testResult.syncAccuracy)}`}>
                    {testResult.syncAccuracy.toFixed(2)}%
                  </span>
                </div>
                <div>
                  <span className="text-sm text-gray-600">平均误差:</span>
                  <span className="ml-2 font-mono">{testResult.avgError.toFixed(2)}ms</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600">最大误差:</span>
                  <span className="ml-2 font-mono">{testResult.maxError.toFixed(2)}ms</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600">测试单词数:</span>
                  <span className="ml-2 font-mono">{testResult.totalWords}</span>
                </div>
              </div>
            </div>
          )}

          {/* 实时监控状态 */}
          {realTimeStatus && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="text-lg font-semibold mb-3">实时监控</h3>
              <div className="flex items-center gap-4">
                <div>
                  <span className="text-sm text-gray-600">当前误差:</span>
                  <span className="ml-2 font-mono">{realTimeStatus.currentError.toFixed(2)}ms</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600">平均误差:</span>
                  <span className="ml-2 font-mono">{realTimeStatus.avgError.toFixed(2)}ms</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600">同步质量:</span>
                  <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${getSyncQualityBadge(realTimeStatus.syncQuality)}`}>
                    {realTimeStatus.syncQuality}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* 使用说明 */}
          <div className="text-sm text-gray-600">
            <h4 className="font-semibold mb-2">使用说明:</h4>
            <ul className="list-disc list-inside space-y-1">
              <li><strong>快速测试:</strong> 使用当前设置测试同步效果</li>
              <li><strong>自动校准:</strong> 自动寻找最佳偏移量参数</li>
              <li><strong>实时监控:</strong> 在朗读过程中实时显示同步状态</li>
              <li><strong>手动调整:</strong> 根据个人喜好微调偏移量</li>
              <li><strong>同步准确度 > 95%:</strong> 完美同步</li>
              <li><strong>同步准确度 85-95%:</strong> 良好同步</li>
              <li><strong>同步准确度 < 85%:</strong> 需要调整</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
