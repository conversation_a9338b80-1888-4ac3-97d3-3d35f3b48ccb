import { useState } from 'react'
import { Bookmark, Plus, Trash2, Edit3, X } from 'lucide-react'
import { BookmarkData } from '../../services/BookService'

interface BookmarkPanelProps {
  bookmarks: BookmarkData[]
  currentParagraph: number
  theme: 'light' | 'dark' | 'sepia'
  onAddBookmark: (title: string, note?: string) => void
  onRemoveBookmark: (bookmarkId: string) => void
  onJumpToBookmark: (paragraphIndex: number) => void
}

export default function BookmarkPanel({
  bookmarks,
  currentParagraph,
  theme,
  onAddBookmark,
  onRemoveBookmark,
  onJumpToBookmark
}: BookmarkPanelProps) {
  const [showAddForm, setShowAddForm] = useState(false)
  const [newBookmarkTitle, setNewBookmarkTitle] = useState('')
  const [newBookmarkNote, setNewBookmarkNote] = useState('')

  const handleAddBookmark = () => {
    if (newBookmarkTitle.trim()) {
      onAddBookmark(newBookmarkTitle.trim(), newBookmarkNote.trim() || undefined)
      setNewBookmarkTitle('')
      setNewBookmarkNote('')
      setShowAddForm(false)
    }
  }

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className={`rounded-lg shadow-sm border p-4 ${
      theme === 'dark' ? 'bg-gray-800 border-gray-700' : 
      theme === 'sepia' ? 'bg-amber-100 border-amber-200' : 'bg-white border-gray-200'
    }`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Bookmark className={`h-5 w-5 mr-2 ${
            theme === 'dark' ? 'text-gray-400' : 
            theme === 'sepia' ? 'text-amber-700' : 'text-gray-500'
          }`} />
          <h3 className={`font-medium ${
            theme === 'dark' ? 'text-white' : 
            theme === 'sepia' ? 'text-amber-900' : 'text-gray-900'
          }`}>
            书签 ({bookmarks.length})
          </h3>
        </div>
        
        <button
          onClick={() => setShowAddForm(true)}
          className={`p-2 rounded-lg transition-colors ${
            theme === 'dark' ? 'hover:bg-gray-700 text-gray-400' : 
            theme === 'sepia' ? 'hover:bg-amber-200 text-amber-700' : 'hover:bg-gray-100 text-gray-500'
          }`}
          title="添加书签"
        >
          <Plus className="h-4 w-4" />
        </button>
      </div>

      {/* 添加书签表单 */}
      {showAddForm && (
        <div className={`mb-4 p-3 rounded-lg border ${
          theme === 'dark' ? 'bg-gray-700 border-gray-600' : 
          theme === 'sepia' ? 'bg-amber-50 border-amber-300' : 'bg-gray-50 border-gray-200'
        }`}>
          <div className="space-y-3">
            <div>
              <label className={`block text-sm font-medium mb-1 ${
                theme === 'dark' ? 'text-gray-300' : 
                theme === 'sepia' ? 'text-amber-800' : 'text-gray-700'
              }`}>
                书签标题
              </label>
              <input
                type="text"
                value={newBookmarkTitle}
                onChange={(e) => setNewBookmarkTitle(e.target.value)}
                placeholder="为当前位置添加书签..."
                className={`w-full px-3 py-2 text-sm border rounded-md ${
                  theme === 'dark'
                    ? 'bg-gray-800 border-gray-600 text-gray-300 placeholder-gray-500'
                    : theme === 'sepia'
                    ? 'bg-amber-100 border-amber-300 text-amber-900 placeholder-amber-600'
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                }`}
                autoFocus
              />
            </div>
            
            <div>
              <label className={`block text-sm font-medium mb-1 ${
                theme === 'dark' ? 'text-gray-300' : 
                theme === 'sepia' ? 'text-amber-800' : 'text-gray-700'
              }`}>
                备注 (可选)
              </label>
              <textarea
                value={newBookmarkNote}
                onChange={(e) => setNewBookmarkNote(e.target.value)}
                placeholder="添加备注..."
                rows={2}
                className={`w-full px-3 py-2 text-sm border rounded-md resize-none ${
                  theme === 'dark'
                    ? 'bg-gray-800 border-gray-600 text-gray-300 placeholder-gray-500'
                    : theme === 'sepia'
                    ? 'bg-amber-100 border-amber-300 text-amber-900 placeholder-amber-600'
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                }`}
              />
            </div>
            
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => {
                  setShowAddForm(false)
                  setNewBookmarkTitle('')
                  setNewBookmarkNote('')
                }}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  theme === 'dark' ? 'hover:bg-gray-600 text-gray-400' : 
                  theme === 'sepia' ? 'hover:bg-amber-200 text-amber-700' : 'hover:bg-gray-200 text-gray-600'
                }`}
              >
                取消
              </button>
              <button
                onClick={handleAddBookmark}
                disabled={!newBookmarkTitle.trim()}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                添加
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 书签列表 */}
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {bookmarks.length === 0 ? (
          <div className={`text-center py-6 text-sm ${
            theme === 'dark' ? 'text-gray-500' : 
            theme === 'sepia' ? 'text-amber-600' : 'text-gray-400'
          }`}>
            还没有添加任何书签
          </div>
        ) : (
          bookmarks
            .sort((a, b) => a.paragraphIndex - b.paragraphIndex)
            .map((bookmark) => (
              <div
                key={bookmark.id}
                className={`p-3 rounded-lg border cursor-pointer transition-colors group ${
                  bookmark.paragraphIndex === currentParagraph
                    ? theme === 'dark'
                      ? 'bg-blue-900/30 border-blue-600'
                      : theme === 'sepia'
                      ? 'bg-orange-200/50 border-orange-400'
                      : 'bg-blue-50 border-blue-300'
                    : theme === 'dark'
                    ? 'bg-gray-700 border-gray-600 hover:bg-gray-600'
                    : theme === 'sepia'
                    ? 'bg-amber-50 border-amber-200 hover:bg-amber-100'
                    : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                }`}
                onClick={() => onJumpToBookmark(bookmark.paragraphIndex)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className={`font-medium text-sm mb-1 truncate ${
                      theme === 'dark' ? 'text-gray-200' : 
                      theme === 'sepia' ? 'text-amber-900' : 'text-gray-900'
                    }`}>
                      {bookmark.title}
                    </h4>
                    
                    <div className={`text-xs mb-1 ${
                      theme === 'dark' ? 'text-gray-400' : 
                      theme === 'sepia' ? 'text-amber-600' : 'text-gray-500'
                    }`}>
                      第 {bookmark.paragraphIndex + 1} 段 • {formatDate(bookmark.createdAt)}
                    </div>
                    
                    {bookmark.note && (
                      <p className={`text-xs mt-1 ${
                        theme === 'dark' ? 'text-gray-400' : 
                        theme === 'sepia' ? 'text-amber-700' : 'text-gray-600'
                      }`}>
                        {bookmark.note}
                      </p>
                    )}
                  </div>
                  
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      onRemoveBookmark(bookmark.id)
                    }}
                    className={`opacity-0 group-hover:opacity-100 p-1 rounded transition-all ${
                      theme === 'dark' ? 'hover:bg-red-900/30 text-red-400' : 
                      theme === 'sepia' ? 'hover:bg-red-100 text-red-600' : 'hover:bg-red-100 text-red-500'
                    }`}
                    title="删除书签"
                  >
                    <Trash2 className="h-3 w-3" />
                  </button>
                </div>
              </div>
            ))
        )}
      </div>
    </div>
  )
}
