import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react'
import { ReadingSettings, ReadingState } from './BookReader'

interface HighPerformanceReadingAreaProps {
  paragraphs: string[]
  settings: ReadingSettings
  readingState: ReadingState
  onProgressChange: (paragraphIndex: number) => void
}

// 文本处理缓存
const textProcessingCache = new Map<
  string,
  {
    sentences: string[]
    words: string[][]
  }
>()

// 缓存文本处理结果
const getCachedTextProcessing = (paragraph: string) => {
  if (textProcessingCache.has(paragraph)) {
    return textProcessingCache.get(paragraph)!
  }

  const sentences = splitIntoSentences(paragraph)
  const words = sentences.map(sentence => splitIntoWords(sentence))

  const result = { sentences, words }
  textProcessingCache.set(paragraph, result)

  // 限制缓存大小
  if (textProcessingCache.size > 1000) {
    const firstKey = textProcessingCache.keys().next().value
    textProcessingCache.delete(firstKey)
  }

  return result
}

// 全局文本处理函数
const splitIntoSentences = (text: string): string[] => {
  const sentences = []
  const regex = /([^.!?。！？]*[.!?。！？]+)/g
  let match
  let lastIndex = 0

  while ((match = regex.exec(text)) !== null) {
    sentences.push(match[1].trim())
    lastIndex = regex.lastIndex
  }

  if (lastIndex < text.length) {
    const remaining = text.substring(lastIndex).trim()
    if (remaining) {
      sentences.push(remaining)
    }
  }

  return sentences.filter(s => s.length > 0)
}

const splitIntoWords = (text: string): string[] => {
  return text.split(/(\s+|[，。！？,.!?;:])/).filter(w => w.length > 0)
}

// 高性能段落组件
const ParagraphItem = React.memo<ParagraphItemProps>(
  ({ index, style, data }) => {
    const {
      paragraphs,
      settings,
      readingState,
      onProgressChange,
      measureHeight,
    } = data
    const elementRef = useRef<HTMLDivElement>(null)
    const paragraph = paragraphs[index]
    const isCurrentParagraph = index === readingState.currentParagraph

    // 测量高度并缓存 - 使用ResizeObserver获得更好的性能
    useEffect(() => {
      const element = elementRef.current
      if (!element) return

      const resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
          const height = entry.contentRect.height
          if (height > 0) {
            measureHeight(index, height)
          }
        }
      })

      resizeObserver.observe(element)
      return () => resizeObserver.disconnect()
    }, [index, measureHeight])

    // 使用缓存的文本处理结果
    const { sentences, words: sentenceWords } = useMemo(
      () => getCachedTextProcessing(paragraph),
      [paragraph]
    )

    return (
      <div
        ref={elementRef}
        style={style}
        className={`mb-6 p-4 rounded-lg transition-all duration-300 cursor-pointer ${
          settings.theme === 'dark'
            ? 'hover:bg-gray-800/50'
            : settings.theme === 'sepia'
              ? 'hover:bg-amber-100/30'
              : 'hover:bg-gray-50'
        }`}
        onClick={() => onProgressChange(index)}
      >
        {sentences.map((sentence, sentenceIndex) => {
          const isCurrentSentence =
            isCurrentParagraph && sentenceIndex === readingState.currentSentence
          const words = sentenceWords[sentenceIndex]

          return (
            <span
              key={sentenceIndex}
              className={`inline-block mr-2 relative ${
                isCurrentSentence && readingState.isPlaying
                  ? 'text-current'
                  : ''
              }`}
            >
              {/* 句子背景高亮 */}
              {isCurrentSentence && readingState.isPlaying && (
                <span
                  className={`absolute inset-0 -inset-x-1 -inset-y-0.5 rounded-lg pointer-events-none ${
                    settings.theme === 'dark'
                      ? 'bg-yellow-400/20'
                      : settings.theme === 'sepia'
                        ? 'bg-yellow-300/40'
                        : 'bg-yellow-200/60'
                  }`}
                  style={{
                    animation: 'fadeIn 0.3s ease-in-out',
                  }}
                />
              )}
              {words.map((word, wordIndex) => {
                const isCurrentWord =
                  isCurrentSentence &&
                  wordIndex === readingState.currentWord &&
                  readingState.isPlaying
                const isSpace = /^\s+$/.test(word)
                const isPunctuation = /^[，。！？,.!?;:]+$/.test(word)

                if (isSpace) {
                  return <span key={wordIndex}> </span>
                }

                const nextWord = words[wordIndex + 1]
                const needsSpace =
                  nextWord &&
                  !isPunctuation &&
                  !/^\s+$/.test(nextWord) &&
                  !/^[，。！？,.!?;:]+$/.test(nextWord)

                return (
                  <React.Fragment key={wordIndex}>
                    <span className="relative inline">
                      {/* 单词背景高亮 */}
                      {isCurrentWord && (
                        <span
                          className={`absolute inset-0 -inset-x-0.5 -inset-y-0.5 rounded pointer-events-none z-10 ${
                            settings.theme === 'dark'
                              ? 'bg-blue-500 shadow-lg'
                              : settings.theme === 'sepia'
                                ? 'bg-blue-600 shadow-lg'
                                : 'bg-blue-600 shadow-lg'
                          }`}
                          style={{
                            animation: 'wordHighlight 0.15s ease-out',
                          }}
                        />
                      )}
                      <span
                        className={`relative z-20 ${
                          isCurrentWord
                            ? 'text-white font-medium'
                            : isCurrentSentence && readingState.isPlaying
                              ? settings.theme === 'dark'
                                ? 'text-yellow-100'
                                : settings.theme === 'sepia'
                                  ? 'text-yellow-800'
                                  : 'text-yellow-800'
                              : ''
                        }`}
                      >
                        {word}
                      </span>
                    </span>
                    {needsSpace && <span> </span>}
                  </React.Fragment>
                )
              })}
              {sentenceIndex < sentences.length - 1 && (
                <span className="inline-block w-2"></span>
              )}
            </span>
          )
        })}
      </div>
    )
  }
)

ParagraphItem.displayName = 'ParagraphItem'

export default function HighPerformanceReadingArea({
  paragraphs,
  settings,
  readingState,
  onProgressChange,
}: HighPerformanceReadingAreaProps) {
  const listRef = useRef<List>(null)
  const heightCache = useRef<Map<number, number>>(new Map())
  const [containerHeight, setContainerHeight] = useState(CONTAINER_HEIGHT)

  // 获取项目高度
  const getItemSize = useCallback((index: number) => {
    return heightCache.current.get(index) || ESTIMATED_PARAGRAPH_HEIGHT
  }, [])

  // 测量并缓存高度
  const measureHeight = useCallback((index: number, height: number) => {
    const currentHeight = heightCache.current.get(index)
    if (currentHeight !== height && height > 0) {
      heightCache.current.set(index, height)
      // 重新计算列表大小
      if (listRef.current) {
        listRef.current.resetAfterIndex(index, false)
      }
    }
  }, [])

  // 自动滚动到当前段落
  useEffect(() => {
    if (listRef.current && readingState.isPlaying) {
      listRef.current.scrollToItem(readingState.currentParagraph, 'center')
    }
  }, [readingState.currentParagraph, readingState.isPlaying])

  // 更新容器高度
  useEffect(() => {
    const updateContainerHeight = () => {
      setContainerHeight(window.innerHeight - 200) // 减去头部和控制栏的高度
    }

    updateContainerHeight()
    window.addEventListener('resize', updateContainerHeight)
    return () => window.removeEventListener('resize', updateContainerHeight)
  }, [])

  // 准备传递给子组件的数据
  const itemData = useMemo(
    () => ({
      paragraphs,
      settings,
      readingState,
      onProgressChange,
      measureHeight,
    }),
    [paragraphs, settings, readingState, onProgressChange, measureHeight]
  )

  return (
    <div
      className={`prose max-w-none ${
        settings.theme === 'dark'
          ? 'prose-invert'
          : settings.theme === 'sepia'
            ? 'prose-amber'
            : ''
      }`}
      style={{
        fontSize: `${settings.fontSize}px`,
        lineHeight: settings.lineHeight,
        fontFamily: settings.fontFamily,
      }}
    >
      <div
        className={`${
          settings.theme === 'dark'
            ? 'text-gray-100'
            : settings.theme === 'sepia'
              ? 'text-amber-900'
              : 'text-gray-900'
        }`}
      >
        <List
          ref={listRef}
          height={containerHeight}
          itemCount={paragraphs.length}
          itemSize={getItemSize}
          itemData={itemData}
          overscanCount={2} // 预渲染2个额外项目以提高滚动性能
          useIsScrolling // 启用滚动状态优化
        >
          {ParagraphItem}
        </List>
      </div>

      {/* 阅读进度指示器 */}
      <div
        className={`fixed bottom-20 right-4 p-3 rounded-lg shadow-lg ${
          settings.theme === 'dark'
            ? 'bg-gray-800 text-white'
            : settings.theme === 'sepia'
              ? 'bg-amber-100 text-amber-900'
              : 'bg-white text-gray-900'
        }`}
      >
        <div className="text-sm font-medium">
          {readingState.currentParagraph + 1} / {paragraphs.length}
        </div>
        <div className="text-xs opacity-75 mt-1">
          {Math.round(
            ((readingState.currentParagraph + 1) / paragraphs.length) * 100
          )}
          %
        </div>
      </div>
    </div>
  )
}
