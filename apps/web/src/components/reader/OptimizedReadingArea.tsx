import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react'
import { ReadingSettings, ReadingState } from './BookReader'

interface OptimizedReadingAreaProps {
  paragraphs: string[]
  settings: ReadingSettings
  readingState: ReadingState
  onProgressChange: (paragraphIndex: number) => void
}

// 文本处理缓存
const textProcessingCache = new Map<
  string,
  {
    sentences: string[]
    words: string[][]
  }
>()

// 缓存文本处理结果
const getCachedTextProcessing = (paragraph: string) => {
  if (textProcessingCache.has(paragraph)) {
    return textProcessingCache.get(paragraph)!
  }

  const sentences = splitIntoSentences(paragraph)
  const words = sentences.map(sentence => splitIntoWords(sentence))

  const result = { sentences, words }
  textProcessingCache.set(paragraph, result)

  // 限制缓存大小
  if (textProcessingCache.size > 500) {
    const firstKey = textProcessingCache.keys().next().value
    textProcessingCache.delete(firstKey)
  }

  return result
}

// 全局文本处理函数
const splitIntoSentences = (text: string): string[] => {
  const sentences = []
  const regex = /([^.!?。！？]*[.!?。！？]+)/g
  let match
  let lastIndex = 0

  while ((match = regex.exec(text)) !== null) {
    sentences.push(match[1].trim())
    lastIndex = regex.lastIndex
  }

  if (lastIndex < text.length) {
    const remaining = text.substring(lastIndex).trim()
    if (remaining) {
      sentences.push(remaining)
    }
  }

  return sentences.filter(s => s.length > 0)
}

const splitIntoWords = (text: string): string[] => {
  return text.split(/(\s+|[，。！？,.!?;:])/).filter(w => w.length > 0)
}

// 高性能段落组件
const ParagraphItemComponent = React.memo<{
  paragraph: string
  index: number
  isCurrentParagraph: boolean
  settings: ReadingSettings
  readingState: ReadingState
  onProgressChange: (index: number) => void
}>(
  ({
    paragraph,
    index,
    isCurrentParagraph,
    settings,
    readingState,
    onProgressChange,
  }) => {
    const elementRef = useRef<HTMLDivElement>(null)

    // 使用缓存的文本处理结果
    const { sentences, words: sentenceWords } = useMemo(
      () => getCachedTextProcessing(paragraph),
      [paragraph]
    )

    return (
      <div
        ref={isCurrentParagraph ? elementRef : null}
        className={`mb-6 p-4 rounded-lg transition-all duration-300 cursor-pointer ${
          settings.theme === 'dark'
            ? 'hover:bg-gray-800/50'
            : settings.theme === 'sepia'
              ? 'hover:bg-amber-100/30'
              : 'hover:bg-gray-50'
        }`}
        onClick={() => onProgressChange(index)}
      >
        {sentences.map((sentence, sentenceIndex) => {
          const isCurrentSentence =
            isCurrentParagraph && sentenceIndex === readingState.currentSentence
          const words = sentenceWords[sentenceIndex]

          return (
            <span
              key={sentenceIndex}
              className="inline-block mr-2 relative"
              data-paragraph={index}
              data-sentence={sentenceIndex}
            >
              {words.map((word, wordIndex) => {
                const isCurrentWord =
                  isCurrentSentence &&
                  wordIndex === readingState.currentWord &&
                  readingState.isPlaying
                const isSpace = /^\s+$/.test(word)
                const isPunctuation = /^[，。！？,.!?;:]+$/.test(word)

                if (isSpace) {
                  return <span key={wordIndex}> </span>
                }

                const nextWord = words[wordIndex + 1]
                const needsSpace =
                  nextWord &&
                  !isPunctuation &&
                  !/^\s+$/.test(nextWord) &&
                  !/^[，。！？,.!?;:]+$/.test(nextWord)

                return (
                  <React.Fragment key={wordIndex}>
                    <span
                      className="inline relative reading-text"
                      data-paragraph={index}
                      data-sentence={sentenceIndex}
                      data-word={wordIndex}
                    >
                      {word}
                    </span>
                    {needsSpace && <span> </span>}
                  </React.Fragment>
                )
              })}
              {sentenceIndex < sentences.length - 1 && (
                <span className="inline-block w-2"></span>
              )}
            </span>
          )
        })}
      </div>
    )
  }
)

ParagraphItemComponent.displayName = 'ParagraphItemComponent'

// 自定义比较函数，只在关键属性变化时重新渲染
const arePropsEqual = (prevProps: any, nextProps: any) => {
  return (
    prevProps.paragraph === nextProps.paragraph &&
    prevProps.index === nextProps.index &&
    prevProps.isCurrentParagraph === nextProps.isCurrentParagraph &&
    prevProps.settings.theme === nextProps.settings.theme &&
    prevProps.settings.fontSize === nextProps.settings.fontSize &&
    prevProps.settings.lineHeight === nextProps.settings.lineHeight &&
    prevProps.settings.fontFamily === nextProps.settings.fontFamily &&
    (prevProps.isCurrentParagraph
      ? prevProps.readingState.currentSentence ===
          nextProps.readingState.currentSentence &&
        prevProps.readingState.currentWord ===
          nextProps.readingState.currentWord &&
        prevProps.readingState.isPlaying === nextProps.readingState.isPlaying
      : true)
  )
}

// 使用自定义比较函数的优化组件
const ParagraphItem = React.memo(ParagraphItemComponent, arePropsEqual)

export default function OptimizedReadingArea({
  paragraphs,
  settings,
  readingState,
  onProgressChange,
}: OptimizedReadingAreaProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const currentParagraphRef = useRef<HTMLDivElement>(null)
  const sentenceHighlightRef = useRef<HTMLDivElement>(null)
  const wordHighlightRef = useRef<HTMLDivElement>(null)
  const [visibleRange, setVisibleRange] = useState({
    start: 0,
    end: Math.min(15, paragraphs.length),
  })

  // 更新浮动高亮位置 - 简化版本
  const updateFloatingHighlights = useCallback(() => {
    if (!readingState.isPlaying || !containerRef.current) {
      // 隐藏高亮
      if (sentenceHighlightRef.current) {
        sentenceHighlightRef.current.style.opacity = '0'
      }
      if (wordHighlightRef.current) {
        wordHighlightRef.current.style.opacity = '0'
      }
      return
    }

    // 使用requestAnimationFrame确保平滑更新
    requestAnimationFrame(() => {
      // 查找当前句子和单词的DOM元素
      const currentSentenceElement = document.querySelector(
        `[data-paragraph="${readingState.currentParagraph}"][data-sentence="${readingState.currentSentence}"]`
      ) as HTMLElement
      const currentWordElement = document.querySelector(
        `[data-paragraph="${readingState.currentParagraph}"][data-sentence="${readingState.currentSentence}"][data-word="${readingState.currentWord}"]`
      ) as HTMLElement

      if (!currentSentenceElement || !currentWordElement) return

      const containerRect = containerRef.current!.getBoundingClientRect()

      // 更新句子高亮
      if (sentenceHighlightRef.current) {
        const rect = currentSentenceElement.getBoundingClientRect()
        const highlight = sentenceHighlightRef.current

        highlight.style.opacity = '1'
        highlight.style.left = `${rect.left - containerRect.left}px`
        highlight.style.top = `${rect.top - containerRect.top}px`
        highlight.style.width = `${rect.width}px`
        highlight.style.height = `${rect.height}px`
      }

      // 更新单词高亮
      if (wordHighlightRef.current) {
        const rect = currentWordElement.getBoundingClientRect()
        const highlight = wordHighlightRef.current

        highlight.style.opacity = '0.8'
        highlight.style.left = `${rect.left - containerRect.left - 2}px`
        highlight.style.top = `${rect.top - containerRect.top - 1}px`
        highlight.style.width = `${rect.width + 4}px`
        highlight.style.height = `${rect.height + 2}px`
      }
    })
  }, [readingState])

  // 监听阅读状态变化，更新高亮位置
  useEffect(() => {
    updateFloatingHighlights()
  }, [readingState, updateFloatingHighlights])

  // 监听滚动和窗口大小变化，更新高亮位置
  useEffect(() => {
    const handleUpdate = () => {
      if (readingState.isPlaying) {
        updateFloatingHighlights()
      }
    }

    window.addEventListener('scroll', handleUpdate, { passive: true })
    window.addEventListener('resize', handleUpdate, { passive: true })

    return () => {
      window.removeEventListener('scroll', handleUpdate)
      window.removeEventListener('resize', handleUpdate)
    }
  }, [readingState.isPlaying, updateFloatingHighlights])

  // 性能监控 (开发环境) - 添加更好的防抖
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const timer = setTimeout(() => {
        console.log(
          `✅ Stable render: paragraphs ${visibleRange.start}-${visibleRange.end} of ${paragraphs.length}`
        )
      }, 500) // 增加防抖时间到500ms
      return () => clearTimeout(timer)
    }
  }, [visibleRange, paragraphs.length])

  // 计算可见范围 - 添加稳定性检查
  const updateVisibleRange = useCallback(() => {
    if (!containerRef.current) return

    const container = containerRef.current
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const containerTop = container.offsetTop
    const containerHeight = window.innerHeight

    // 计算当前可见的段落范围
    const buffer = 8 // 增加缓冲区大小以减少频繁更新
    const estimatedParagraphHeight = 120

    const startIndex = Math.max(
      0,
      Math.floor((scrollTop - containerTop) / estimatedParagraphHeight) - buffer
    )
    const endIndex = Math.min(
      paragraphs.length,
      startIndex +
        Math.ceil(containerHeight / estimatedParagraphHeight) +
        buffer * 2
    )

    // 只有当范围变化足够大时才更新，避免微小变化导致的抖动
    setVisibleRange(prev => {
      const startDiff = Math.abs(prev.start - startIndex)
      const endDiff = Math.abs(prev.end - endIndex)

      // 如果变化小于3个段落，则不更新
      if (startDiff < 3 && endDiff < 3) {
        return prev
      }

      return { start: startIndex, end: endIndex }
    })
  }, [paragraphs.length])

  // 监听滚动事件 - 使用防抖和节流优化性能
  useEffect(() => {
    let ticking = false
    let debounceTimer: NodeJS.Timeout | null = null

    const handleScroll = () => {
      // 清除之前的防抖定时器
      if (debounceTimer) {
        clearTimeout(debounceTimer)
      }

      // 立即节流处理
      if (!ticking) {
        requestAnimationFrame(() => {
          updateVisibleRange()
          ticking = false
        })
        ticking = true
      }

      // 防抖处理，确保滚动停止后再次更新
      debounceTimer = setTimeout(() => {
        updateVisibleRange()
      }, 150)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    window.addEventListener('resize', handleScroll, { passive: true })
    updateVisibleRange() // 初始计算

    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', handleScroll)
      if (debounceTimer) {
        clearTimeout(debounceTimer)
      }
    }
  }, [updateVisibleRange])

  // 自动滚动到当前段落
  useEffect(() => {
    if (readingState.isPlaying && currentParagraphRef.current) {
      currentParagraphRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      })
    }
  }, [readingState.currentParagraph, readingState.isPlaying])

  // 渲染可见段落 - 优化稳定性
  const visibleParagraphs = useMemo(() => {
    const items = []
    const estimatedParagraphHeight = 120

    // 添加占位符以保持正确的滚动位置
    if (visibleRange.start > 0) {
      const estimatedHeight = visibleRange.start * estimatedParagraphHeight
      items.push(<div key="spacer-top" style={{ height: estimatedHeight }} />)
    }

    // 渲染可见段落
    for (let i = visibleRange.start; i < visibleRange.end; i++) {
      if (i >= 0 && i < paragraphs.length) {
        const isCurrentParagraph = i === readingState.currentParagraph
        items.push(
          <div
            key={`paragraph-${i}`}
            ref={isCurrentParagraph ? currentParagraphRef : null}
          >
            <ParagraphItem
              paragraph={paragraphs[i]}
              index={i}
              isCurrentParagraph={isCurrentParagraph}
              settings={settings}
              readingState={readingState}
              onProgressChange={onProgressChange}
            />
          </div>
        )
      }
    }

    // 添加底部占位符
    if (visibleRange.end < paragraphs.length) {
      const remainingHeight =
        (paragraphs.length - visibleRange.end) * estimatedParagraphHeight
      items.push(
        <div key="spacer-bottom" style={{ height: remainingHeight }} />
      )
    }

    return items
  }, [visibleRange, paragraphs, settings, readingState, onProgressChange])

  return (
    <div
      ref={containerRef}
      className={`prose max-w-none relative ${
        settings.theme === 'dark'
          ? 'prose-invert'
          : settings.theme === 'sepia'
            ? 'prose-amber'
            : ''
      }`}
      style={{
        fontSize: `${settings.fontSize}px`,
        lineHeight: settings.lineHeight,
        fontFamily: settings.fontFamily,
      }}
    >
      <div
        className={`${
          settings.theme === 'dark'
            ? 'text-gray-100'
            : settings.theme === 'sepia'
              ? 'text-amber-900'
              : 'text-gray-900'
        }`}
      >
        {visibleParagraphs}
      </div>

      {/* 浮动高亮层 - 相对于容器定位 */}
      <div className="absolute inset-0 pointer-events-none z-10 overflow-hidden">
        {/* 句子高亮 */}
        <div
          ref={sentenceHighlightRef}
          className={`absolute floating-sentence-highlight transition-opacity duration-200 ${
            settings.theme === 'dark'
              ? 'bg-yellow-400/20'
              : settings.theme === 'sepia'
                ? 'bg-yellow-300/40'
                : 'bg-yellow-200/60'
          }`}
          style={{
            opacity: 0,
          }}
        />

        {/* 单词高亮 */}
        <div
          ref={wordHighlightRef}
          className={`absolute floating-word-highlight transition-opacity duration-150 ${
            settings.theme === 'dark'
              ? 'bg-blue-500/80'
              : settings.theme === 'sepia'
                ? 'bg-blue-600/80'
                : 'bg-blue-500/80'
          }`}
          style={{
            opacity: 0,
          }}
        />
      </div>

      {/* 阅读进度指示器 */}
      <div
        className={`fixed bottom-20 right-4 p-3 rounded-lg shadow-lg z-50 ${
          settings.theme === 'dark'
            ? 'bg-gray-800 text-white'
            : settings.theme === 'sepia'
              ? 'bg-amber-100 text-amber-900'
              : 'bg-white text-gray-900'
        }`}
      >
        <div className="text-sm font-medium">
          {readingState.currentParagraph + 1} / {paragraphs.length}
        </div>
        <div className="text-xs opacity-75 mt-1">
          {Math.round(
            ((readingState.currentParagraph + 1) / paragraphs.length) * 100
          )}
          %
        </div>
      </div>
    </div>
  )
}
