import React, { useState, useEffect } from 'react'
import { enhancedTTSService } from '../../services/speech/EnhancedTTSService'
import { SyncDebugPanel } from '../debug/SyncDebugPanel'
import { optimizeSync } from '../../utils/syncTester'

interface PerfectSyncReaderProps {
  text: string
  onWordHighlight?: (wordIndex: number, charIndex: number) => void
}

export const PerfectSyncReader: React.FC<PerfectSyncReaderProps> = ({ 
  text, 
  onWordHighlight 
}) => {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentWordIndex, setCurrentWordIndex] = useState(-1)
  const [showDebugPanel, setShowDebugPanel] = useState(false)
  const [syncOptimized, setSyncOptimized] = useState(false)
  const [words, setWords] = useState<string[]>([])

  useEffect(() => {
    // 分词处理
    const wordList = text.match(/\S+/g) || []
    setWords(wordList)
  }, [text])

  useEffect(() => {
    // 监听TTS事件
    const handleSpeechEvent = (event: any) => {
      switch (event.type) {
        case 'start':
          setIsPlaying(true)
          break
        case 'end':
          setIsPlaying(false)
          setCurrentWordIndex(-1)
          break
        case 'boundary':
          if (event.charIndex !== undefined) {
            // 计算当前单词索引
            const wordIndex = findWordIndexByCharIndex(event.charIndex)
            setCurrentWordIndex(wordIndex)
            onWordHighlight?.(wordIndex, event.charIndex)
          }
          break
      }
    }

    enhancedTTSService.addEventListener(handleSpeechEvent)
    return () => enhancedTTSService.removeEventListener(handleSpeechEvent)
  }, [text, onWordHighlight])

  const findWordIndexByCharIndex = (charIndex: number): number => {
    let currentIndex = 0
    for (let i = 0; i < words.length; i++) {
      const wordStart = currentIndex
      const wordEnd = currentIndex + words[i].length
      
      if (charIndex >= wordStart && charIndex < wordEnd) {
        return i
      }
      
      // 加上单词长度和一个空格
      currentIndex = wordEnd + 1
    }
    return words.length - 1
  }

  const handlePlay = async () => {
    if (isPlaying) {
      enhancedTTSService.pause()
      setIsPlaying(false)
    } else {
      // 如果还没有优化过同步，先进行一次快速优化
      if (!syncOptimized) {
        try {
          await optimizeSync(text.substring(0, 100)) // 使用前100个字符进行快速校准
          setSyncOptimized(true)
        } catch (error) {
          console.warn('Sync optimization failed, using default settings:', error)
        }
      }
      
      enhancedTTSService.speak(text)
    }
  }

  const handleStop = () => {
    enhancedTTSService.stop()
    setIsPlaying(false)
    setCurrentWordIndex(-1)
  }

  const handleOptimizeSync = async () => {
    try {
      const result = await optimizeSync(text.substring(0, 200))
      console.log('Sync optimization result:', result)
      setSyncOptimized(true)
      alert(`同步优化完成！准确度提升了 ${result.improvement.toFixed(2)}%`)
    } catch (error) {
      console.error('Sync optimization failed:', error)
      alert('同步优化失败，请检查控制台')
    }
  }

  return (
    <div className="perfect-sync-reader">
      {/* 控制面板 */}
      <div className="mb-4 flex items-center gap-3">
        <button
          onClick={handlePlay}
          className={`px-4 py-2 rounded-lg font-medium ${
            isPlaying 
              ? 'bg-red-600 text-white hover:bg-red-700' 
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isPlaying ? '暂停' : '播放'}
        </button>
        
        <button
          onClick={handleStop}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
        >
          停止
        </button>
        
        <button
          onClick={handleOptimizeSync}
          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          title="自动优化同步效果"
        >
          {syncOptimized ? '重新优化' : '优化同步'}
        </button>
        
        <button
          onClick={() => setShowDebugPanel(true)}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
          title="打开同步调试面板"
        >
          调试面板
        </button>
        
        {syncOptimized && (
          <span className="text-green-600 text-sm font-medium">
            ✓ 已优化
          </span>
        )}
      </div>

      {/* 文本显示区域 */}
      <div className="text-display p-6 bg-gray-50 rounded-lg leading-relaxed">
        {words.map((word, index) => (
          <span
            key={index}
            className={`inline-block mr-2 mb-1 px-1 py-0.5 rounded transition-all duration-150 ${
              index === currentWordIndex
                ? 'bg-blue-600 text-white font-medium shadow-lg transform scale-105'
                : isPlaying && index < currentWordIndex
                ? 'bg-yellow-200 text-gray-800'
                : 'text-gray-800'
            }`}
            style={{
              // 添加平滑的高亮动画
              transition: index === currentWordIndex 
                ? 'all 0.1s cubic-bezier(0.4, 0, 0.2, 1)' 
                : 'all 0.2s ease-out'
            }}
          >
            {word}
          </span>
        ))}
      </div>

      {/* 同步状态指示器 */}
      {isPlaying && (
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
              <span className="text-sm text-blue-800">
                正在朗读... 当前单词: {currentWordIndex >= 0 ? words[currentWordIndex] : ''}
              </span>
            </div>
            <div className="text-xs text-blue-600">
              进度: {currentWordIndex + 1} / {words.length}
            </div>
          </div>
        </div>
      )}

      {/* 使用提示 */}
      <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
        <h4 className="font-semibold text-yellow-800 mb-2">完美同步功能说明:</h4>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• <strong>自动优化:</strong> 首次播放前会自动校准同步参数</li>
          <li>• <strong>实时调整:</strong> 播放过程中会根据实际情况动态调整</li>
          <li>• <strong>高频同步:</strong> 每5ms检查一次，确保精确同步</li>
          <li>• <strong>预测性高亮:</strong> 提前预测下一个单词，减少延迟</li>
          <li>• <strong>调试面板:</strong> 可以手动调整参数和查看同步状态</li>
        </ul>
      </div>

      {/* 调试面板 */}
      <SyncDebugPanel 
        isOpen={showDebugPanel} 
        onClose={() => setShowDebugPanel(false)} 
      />
    </div>
  )
}

// 使用示例组件
export const PerfectSyncReaderDemo: React.FC = () => {
  const sampleText = `
    The quick brown fox jumps over the lazy dog. This is a demonstration of perfect synchronization 
    between text-to-speech audio and word highlighting. The system uses advanced algorithms to ensure 
    frame-perfect timing, with automatic calibration and real-time adjustments. Every word should be 
    highlighted at exactly the right moment when it's being spoken.
  `.trim()

  const [highlightedWord, setHighlightedWord] = useState<{index: number, char: number} | null>(null)

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">完美同步TTS阅读器演示</h1>
      
      <PerfectSyncReader 
        text={sampleText}
        onWordHighlight={(wordIndex, charIndex) => {
          setHighlightedWord({ index: wordIndex, char: charIndex })
        }}
      />
      
      {highlightedWord && (
        <div className="mt-4 p-3 bg-gray-100 rounded-lg">
          <div className="text-sm text-gray-600">
            当前高亮: 单词索引 {highlightedWord.index}, 字符位置 {highlightedWord.char}
          </div>
        </div>
      )}
    </div>
  )
}
