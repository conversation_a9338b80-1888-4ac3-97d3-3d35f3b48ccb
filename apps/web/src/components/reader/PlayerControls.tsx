import {
  <PERSON>,
  Pause,
  Square,
  <PERSON><PERSON><PERSON><PERSON>,
  Ski<PERSON><PERSON><PERSON><PERSON>,
  Volume2,
} from 'lucide-react'
import { ReadingState } from './BookReader'
import VoiceDropdown from './VoiceDropdown'
import { TTSVoice } from '../../services/speech/EnhancedTTSService'

interface PlayerControlsProps {
  readingState: ReadingState
  totalParagraphs: number
  onPlayPause: () => void
  onStop: () => void
  onSpeedChange: (speed: number) => void
  onVolumeChange: (volume: number) => void
  onProgressChange: (paragraphIndex: number) => void
  theme: 'light' | 'dark' | 'sepia'
  voices: TTSVoice[]
  currentVoice: TTSVoice | null
  onVoiceChange: (voice: TTSVoice) => void
}

export default function PlayerControls({
  readingState,
  totalParagraphs,
  onPlayPause,
  onStop,
  onSpeedChange,
  onVolumeChange,
  onProgressChange,
  theme,
  voices,
  currentVoice,
  onVoiceChange,
}: PlayerControlsProps) {
  const progress =
    totalParagraphs > 0
      ? (readingState.currentParagraph / totalParagraphs) * 100
      : 0

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const percentage = clickX / rect.width
    const paragraphIndex = Math.floor(percentage * totalParagraphs)
    onProgressChange(Math.max(0, Math.min(paragraphIndex, totalParagraphs - 1)))
  }

  const handlePrevious = () => {
    if (readingState.currentParagraph > 0) {
      onProgressChange(readingState.currentParagraph - 1)
    }
  }

  const handleNext = () => {
    if (readingState.currentParagraph < totalParagraphs - 1) {
      onProgressChange(readingState.currentParagraph + 1)
    }
  }

  const speedOptions = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]

  return (
    <div
      className={`fixed bottom-0 left-0 right-0 border-t backdrop-blur-sm z-40 ${
        theme === 'dark'
          ? 'bg-gray-900/95 border-gray-700'
          : theme === 'sepia'
            ? 'bg-amber-50/95 border-amber-200'
            : 'bg-white/95 border-gray-200'
      }`}
    >
      <div className="max-w-6xl mx-auto px-4 py-2">
        {/* 进度条 */}
        <div className="mb-3">
          <div
            className={`h-2 rounded-full cursor-pointer ${
              theme === 'dark'
                ? 'bg-gray-700'
                : theme === 'sepia'
                  ? 'bg-amber-200'
                  : 'bg-gray-200'
            }`}
            onClick={handleProgressClick}
          >
            <div
              className="h-full bg-blue-500 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <div
            className={`flex justify-between text-xs mt-1 ${
              theme === 'dark'
                ? 'text-gray-400'
                : theme === 'sepia'
                  ? 'text-amber-700'
                  : 'text-gray-500'
            }`}
          >
            <span>段落 {readingState.currentParagraph + 1}</span>
            <span>共 {totalParagraphs} 段</span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          {/* 左侧控制按钮 */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handlePrevious}
              disabled={readingState.currentParagraph === 0}
              className={`p-2 rounded-full transition-colors ${
                readingState.currentParagraph === 0
                  ? 'opacity-50 cursor-not-allowed'
                  : theme === 'dark'
                    ? 'hover:bg-gray-800 text-gray-300'
                    : theme === 'sepia'
                      ? 'hover:bg-amber-100 text-amber-800'
                      : 'hover:bg-gray-100 text-gray-600'
              }`}
            >
              <SkipBack className="h-5 w-5" />
            </button>

            <button
              onClick={onPlayPause}
              className={`p-3 rounded-full transition-colors ${
                theme === 'dark'
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : theme === 'sepia'
                    ? 'bg-orange-500 hover:bg-orange-600 text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {readingState.isPlaying ? (
                <Pause className="h-6 w-6" />
              ) : (
                <Play className="h-6 w-6" />
              )}
            </button>

            <button
              onClick={onStop}
              className={`p-2 rounded-full transition-colors ${
                theme === 'dark'
                  ? 'hover:bg-gray-800 text-gray-300'
                  : theme === 'sepia'
                    ? 'hover:bg-amber-100 text-amber-800'
                    : 'hover:bg-gray-100 text-gray-600'
              }`}
            >
              <Square className="h-5 w-5" />
            </button>

            <button
              onClick={handleNext}
              disabled={readingState.currentParagraph === totalParagraphs - 1}
              className={`p-2 rounded-full transition-colors ${
                readingState.currentParagraph === totalParagraphs - 1
                  ? 'opacity-50 cursor-not-allowed'
                  : theme === 'dark'
                    ? 'hover:bg-gray-800 text-gray-300'
                    : theme === 'sepia'
                      ? 'hover:bg-amber-100 text-amber-800'
                      : 'hover:bg-gray-100 text-gray-600'
              }`}
            >
              <SkipForward className="h-5 w-5" />
            </button>
          </div>

          {/* 中间信息显示 */}
          <div
            className={`text-center ${
              theme === 'dark'
                ? 'text-gray-300'
                : theme === 'sepia'
                  ? 'text-amber-800'
                  : 'text-gray-700'
            }`}
          >
            <div className="text-sm font-medium">
              {readingState.isPlaying ? '正在播放' : '已暂停'}
            </div>
            <div className="text-xs opacity-75">
              {Math.round(progress)}% 完成
            </div>
          </div>

          {/* 右侧控制选项 */}
          <div className="flex items-center space-x-4">
            {/* 语速控制 */}
            <div className="flex items-center space-x-2">
              <span
                className={`text-sm ${
                  theme === 'dark'
                    ? 'text-gray-400'
                    : theme === 'sepia'
                      ? 'text-amber-700'
                      : 'text-gray-500'
                }`}
              >
                语速
              </span>
              <select
                value={readingState.playbackRate}
                onChange={e => onSpeedChange(parseFloat(e.target.value))}
                className={`text-sm px-2 py-1 rounded border ${
                  theme === 'dark'
                    ? 'bg-gray-800 border-gray-600 text-gray-300'
                    : theme === 'sepia'
                      ? 'bg-amber-100 border-amber-300 text-amber-900'
                      : 'bg-white border-gray-300 text-gray-700'
                }`}
              >
                {speedOptions.map(speed => (
                  <option key={speed} value={speed}>
                    {speed}x
                  </option>
                ))}
              </select>
            </div>

            {/* 音量控制 */}
            <div className="flex items-center space-x-2">
              <Volume2
                className={`h-4 w-4 ${
                  theme === 'dark'
                    ? 'text-gray-400'
                    : theme === 'sepia'
                      ? 'text-amber-700'
                      : 'text-gray-500'
                }`}
              />
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={readingState.volume}
                onChange={e => onVolumeChange(parseFloat(e.target.value))}
                className="w-20"
              />
              <span
                className={`text-xs w-8 ${
                  theme === 'dark'
                    ? 'text-gray-400'
                    : theme === 'sepia'
                      ? 'text-amber-700'
                      : 'text-gray-500'
                }`}
              >
                {Math.round(readingState.volume * 100)}%
              </span>
            </div>
          </div>
        </div>
        {/* 语音选择下拉 */}
        <div className="mt-2">
          <VoiceDropdown
            voices={voices}
            currentVoice={currentVoice}
            onVoiceChange={onVoiceChange}
            theme={theme}
          />
        </div>
      </div>
    </div>
  )
}
