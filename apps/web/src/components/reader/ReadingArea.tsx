import React, { useEffect, useRef } from 'react'
import { ReadingSettings, ReadingState } from './BookReader'

interface ReadingAreaProps {
  paragraphs: string[]
  settings: ReadingSettings
  readingState: ReadingState
  onProgressChange: (paragraphIndex: number) => void
}

export default function ReadingArea({
  paragraphs,
  settings,
  readingState,
  onProgressChange,
}: ReadingAreaProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const currentParagraphRef = useRef<HTMLDivElement>(null)

  // 自动滚动到当前段落
  useEffect(() => {
    if (currentParagraphRef.current && readingState.isPlaying) {
      currentParagraphRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      })
    }
  }, [readingState.currentParagraph, readingState.isPlaying])

  const splitIntoSentences = (text: string): string[] => {
    // 改进的句子分割，保留标点符号
    const sentences = []
    const regex = /([^.!?。！？]*[.!?。！？]+)/g
    let match
    let lastIndex = 0

    while ((match = regex.exec(text)) !== null) {
      sentences.push(match[1].trim())
      lastIndex = regex.lastIndex
    }

    // 添加剩余的文本（如果没有以标点结尾）
    if (lastIndex < text.length) {
      const remaining = text.substring(lastIndex).trim()
      if (remaining) {
        sentences.push(remaining)
      }
    }

    return sentences.filter(s => s.length > 0)
  }

  const splitIntoWords = (text: string): string[] => {
    // 改进的单词分割，支持中英文和标点符号，保留空格
    // 使用更精确的分割方式，确保单词间的空格被正确保留
    return text.split(/(\s+|[，。！？,.!?;:])/).filter(w => w.length > 0)
  }

  const renderParagraph = (paragraph: string, paragraphIndex: number) => {
    const isCurrentParagraph = paragraphIndex === readingState.currentParagraph
    const sentences = splitIntoSentences(paragraph)

    return (
      <div
        key={paragraphIndex}
        ref={isCurrentParagraph ? currentParagraphRef : null}
        className={`mb-6 p-4 rounded-lg transition-all duration-300 cursor-pointer ${
          settings.theme === 'dark'
            ? 'hover:bg-gray-800/50'
            : settings.theme === 'sepia'
              ? 'hover:bg-amber-100/30'
              : 'hover:bg-gray-50'
        }`}
        onClick={() => onProgressChange(paragraphIndex)}
      >
        {sentences.map((sentence, sentenceIndex) => {
          const isCurrentSentence =
            isCurrentParagraph && sentenceIndex === readingState.currentSentence
          const words = splitIntoWords(sentence)

          return (
            <span
              key={sentenceIndex}
              className={`inline-block mr-2 relative ${
                isCurrentSentence && readingState.isPlaying
                  ? 'text-current'
                  : ''
              }`}
            >
              {/* 句子背景高亮 - 浮动覆盖 */}
              {isCurrentSentence && readingState.isPlaying && (
                <span
                  className={`absolute inset-0 -inset-x-1 -inset-y-0.5 rounded-lg pointer-events-none ${
                    settings.theme === 'dark'
                      ? 'bg-yellow-400/20'
                      : settings.theme === 'sepia'
                        ? 'bg-yellow-300/40'
                        : 'bg-yellow-200/60'
                  }`}
                  style={{
                    animation: 'fadeIn 0.3s ease-in-out',
                  }}
                />
              )}
              {words.map((word, wordIndex) => {
                const isCurrentWord =
                  isCurrentSentence &&
                  wordIndex === readingState.currentWord &&
                  readingState.isPlaying
                const isSpace = /^\s+$/.test(word)
                const isPunctuation = /^[，。！？,.!?;:]+$/.test(word)

                if (isSpace) {
                  return <span key={wordIndex}> </span>
                }

                const nextWord = words[wordIndex + 1]
                const needsSpace =
                  nextWord &&
                  !isPunctuation &&
                  !/^\s+$/.test(nextWord) &&
                  !/^[，。！？,.!?;:]+$/.test(nextWord)

                return (
                  <React.Fragment key={wordIndex}>
                    <span className="relative inline">
                      {/* 单词背景高亮 - 浮动覆盖 */}
                      {isCurrentWord && (
                        <span
                          className={`absolute inset-0 -inset-x-0.5 -inset-y-0.5 rounded pointer-events-none z-10 ${
                            settings.theme === 'dark'
                              ? 'bg-blue-500 shadow-lg'
                              : settings.theme === 'sepia'
                                ? 'bg-blue-600 shadow-lg'
                                : 'bg-blue-600 shadow-lg'
                          }`}
                          style={{
                            animation: 'wordHighlight 0.15s ease-out',
                          }}
                        />
                      )}
                      {/* 原始文本 - 保持不变 */}
                      <span
                        className={`relative z-20 ${
                          isCurrentWord
                            ? 'text-white font-medium'
                            : isCurrentSentence && readingState.isPlaying
                              ? settings.theme === 'dark'
                                ? 'text-yellow-100'
                                : settings.theme === 'sepia'
                                  ? 'text-yellow-800'
                                  : 'text-yellow-800'
                              : ''
                        }`}
                      >
                        {word}
                      </span>
                    </span>
                    {needsSpace && <span> </span>}
                  </React.Fragment>
                )
              })}
              {sentenceIndex < sentences.length - 1 && (
                <span className="inline-block w-2"></span>
              )}
            </span>
          )
        })}
      </div>
    )
  }

  return (
    <div
      ref={containerRef}
      className={`prose max-w-none ${
        settings.theme === 'dark'
          ? 'prose-invert'
          : settings.theme === 'sepia'
            ? 'prose-amber'
            : ''
      }`}
      style={{
        fontSize: `${settings.fontSize}px`,
        lineHeight: settings.lineHeight,
        fontFamily: settings.fontFamily,
      }}
    >
      <div
        className={`${
          settings.theme === 'dark'
            ? 'text-gray-100'
            : settings.theme === 'sepia'
              ? 'text-amber-900'
              : 'text-gray-900'
        }`}
      >
        {paragraphs.map((paragraph, index) =>
          renderParagraph(paragraph, index)
        )}
      </div>

      {/* 阅读进度指示器 */}
      <div
        className={`fixed bottom-20 right-4 p-3 rounded-lg shadow-lg ${
          settings.theme === 'dark'
            ? 'bg-gray-800 text-white'
            : settings.theme === 'sepia'
              ? 'bg-amber-100 text-amber-900'
              : 'bg-white text-gray-900'
        }`}
      >
        <div className="text-sm font-medium">
          {readingState.currentParagraph + 1} / {paragraphs.length}
        </div>
        <div className="text-xs opacity-75 mt-1">
          {Math.round(
            ((readingState.currentParagraph + 1) / paragraphs.length) * 100
          )}
          %
        </div>
      </div>
    </div>
  )
}
