import { TTSVoice } from '../../services/speech/EnhancedTTSService'

interface VoiceDropdownProps {
  voices: TTSVoice[]
  currentVoice: TTSVoice | null
  onVoiceChange: (voice: TTSVoice) => void
  theme: 'light' | 'dark' | 'sepia'
}

export default function VoiceDropdown({ voices, currentVoice, onVoiceChange, theme }: VoiceDropdownProps) {
  if (!voices || voices.length === 0) {
    return <div className="text-xs opacity-70">正在加载语音...</div>
  }

  const sorted = [...voices].sort((a, b) => {
    if (a.provider !== b.provider) return a.provider === 'edge-tts' ? -1 : 1
    if (a.language !== b.language) return a.language.localeCompare(b.language)
    return a.name.localeCompare(b.name)
  })

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const v = sorted.find(v => v.id === e.target.value)
    if (v) onVoiceChange(v)
  }

  return (
    <div className="flex items-center space-x-2 min-w-[260px]">
      <span className={`text-sm ${
        theme === 'dark' ? 'text-gray-400' : theme === 'sepia' ? 'text-amber-700' : 'text-gray-600'
      }`}>语音</span>
      <select
        value={currentVoice?.id || ''}
        onChange={handleChange}
        className={`flex-1 text-sm px-2 py-1 rounded border max-w-full overflow-hidden text-ellipsis ${
          theme === 'dark' ? 'bg-gray-800 border-gray-600 text-gray-200' :
          theme === 'sepia' ? 'bg-amber-100 border-amber-300 text-amber-900' :
          'bg-white border-gray-300 text-gray-800'
        }`}
      >
        <optgroup label="Microsoft Edge TTS">
          {sorted.filter(v => v.provider === 'edge-tts').map(v => (
            <option key={v.id} value={v.id}>
              {v.name} · {v.language}
            </option>
          ))}
        </optgroup>
        <optgroup label="浏览器内置">
          {sorted.filter(v => v.provider === 'browser').map(v => (
            <option key={v.id} value={v.id}>
              {v.name} · {v.language}
            </option>
          ))}
        </optgroup>
      </select>
    </div>
  )
}

