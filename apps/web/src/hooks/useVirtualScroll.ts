import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react'

interface VirtualItem {
  index: number
  height: number
  offset: number
}

interface UseVirtualScrollOptions {
  itemCount: number
  estimatedItemHeight: number
  containerHeight: number
  bufferSize?: number
  scrollTop: number
}

interface UseVirtualScrollReturn {
  virtualItems: VirtualItem[]
  visibleRange: { start: number; end: number }
  totalHeight: number
  measureItem: (index: number, height: number) => void
  scrollToIndex: (index: number, behavior?: ScrollBehavior) => void
}

export function useVirtualScroll({
  itemCount,
  estimatedItemHeight,
  containerHeight,
  bufferSize = 5,
  scrollTop,
}: UseVirtualScrollOptions): UseVirtualScrollReturn {
  const measurementCache = useRef<Map<number, number>>(new Map())
  const [, forceUpdate] = useState({})

  // 强制重新渲染
  const triggerUpdate = useCallback(() => {
    forceUpdate({})
  }, [])

  // 计算虚拟项目
  const virtualItems = useMemo(() => {
    const items: VirtualItem[] = []
    let offset = 0

    for (let i = 0; i < itemCount; i++) {
      const height = measurementCache.current.get(i) || estimatedItemHeight
      items.push({
        index: i,
        height,
        offset,
      })
      offset += height
    }

    return items
  }, [itemCount, estimatedItemHeight, measurementCache.current.size])

  // 计算可见范围
  const visibleRange = useMemo(() => {
    if (virtualItems.length === 0) {
      return { start: 0, end: 0 }
    }

    let start = 0
    let end = virtualItems.length - 1

    // 找到第一个可见项目
    for (let i = 0; i < virtualItems.length; i++) {
      if (virtualItems[i].offset + virtualItems[i].height > scrollTop) {
        start = Math.max(0, i - bufferSize)
        break
      }
    }

    // 找到最后一个可见项目
    for (let i = start; i < virtualItems.length; i++) {
      if (virtualItems[i].offset > scrollTop + containerHeight) {
        end = Math.min(virtualItems.length - 1, i + bufferSize)
        break
      }
    }

    return { start, end }
  }, [virtualItems, scrollTop, containerHeight, bufferSize])

  // 总高度
  const totalHeight = useMemo(() => {
    if (virtualItems.length === 0) return 0
    const lastItem = virtualItems[virtualItems.length - 1]
    return lastItem.offset + lastItem.height
  }, [virtualItems])

  // 测量项目高度
  const measureItem = useCallback((index: number, height: number) => {
    const currentHeight = measurementCache.current.get(index)
    if (currentHeight !== height && height > 0) {
      measurementCache.current.set(index, height)
      // 延迟触发更新以避免频繁重渲染
      setTimeout(triggerUpdate, 0)
    }
  }, [triggerUpdate])

  // 滚动到指定索引
  const scrollToIndex = useCallback((index: number, behavior: ScrollBehavior = 'smooth') => {
    if (index < 0 || index >= virtualItems.length) return

    const item = virtualItems[index]
    if (item) {
      const targetScrollTop = item.offset - containerHeight / 2 + item.height / 2
      return Math.max(0, targetScrollTop)
    }
    return 0
  }, [virtualItems, containerHeight])

  return {
    virtualItems,
    visibleRange,
    totalHeight,
    measureItem,
    scrollToIndex,
  }
}

// 高性能的段落渲染器组件
interface VirtualParagraphProps {
  paragraph: string
  index: number
  isCurrentParagraph: boolean
  settings: any
  readingState: any
  onProgressChange: (index: number) => void
  onMeasure: (index: number, height: number) => void
  style: React.CSSProperties
}

export const VirtualParagraph = React.memo<VirtualParagraphProps>(({
  paragraph,
  index,
  isCurrentParagraph,
  settings,
  readingState,
  onProgressChange,
  onMeasure,
  style,
}) => {
  const elementRef = useRef<HTMLDivElement>(null)

  // 测量高度
  useEffect(() => {
    if (elementRef.current) {
      const height = elementRef.current.offsetHeight
      if (height > 0) {
        onMeasure(index, height)
      }
    }
  })

  // 分割句子
  const splitIntoSentences = (text: string): string[] => {
    const sentences = []
    const regex = /([^.!?。！？]*[.!?。！？]+)/g
    let match
    let lastIndex = 0

    while ((match = regex.exec(text)) !== null) {
      sentences.push(match[1].trim())
      lastIndex = regex.lastIndex
    }

    if (lastIndex < text.length) {
      const remaining = text.substring(lastIndex).trim()
      if (remaining) {
        sentences.push(remaining)
      }
    }

    return sentences.filter(s => s.length > 0)
  }

  // 分割单词
  const splitIntoWords = (text: string): string[] => {
    return text.split(/(\s+|[，。！？,.!?;:])/).filter(w => w.length > 0)
  }

  const sentences = splitIntoSentences(paragraph)

  return (
    <div
      ref={elementRef}
      style={style}
      className={`mb-6 p-4 rounded-lg transition-all duration-300 cursor-pointer ${
        settings.theme === 'dark'
          ? 'hover:bg-gray-800/50'
          : settings.theme === 'sepia'
            ? 'hover:bg-amber-100/30'
            : 'hover:bg-gray-50'
      }`}
      onClick={() => onProgressChange(index)}
    >
      {sentences.map((sentence, sentenceIndex) => {
        const isCurrentSentence =
          isCurrentParagraph && sentenceIndex === readingState.currentSentence
        const words = splitIntoWords(sentence)

        return (
          <span
            key={sentenceIndex}
            className={`inline-block mr-2 relative ${
              isCurrentSentence && readingState.isPlaying
                ? 'text-current'
                : ''
            }`}
          >
            {/* 句子背景高亮 */}
            {isCurrentSentence && readingState.isPlaying && (
              <span
                className={`absolute inset-0 -inset-x-1 -inset-y-0.5 rounded-lg pointer-events-none ${
                  settings.theme === 'dark'
                    ? 'bg-yellow-400/20'
                    : settings.theme === 'sepia'
                      ? 'bg-yellow-300/40'
                      : 'bg-yellow-200/60'
                }`}
                style={{
                  animation: 'fadeIn 0.3s ease-in-out',
                }}
              />
            )}
            {words.map((word, wordIndex) => {
              const isCurrentWord =
                isCurrentSentence &&
                wordIndex === readingState.currentWord &&
                readingState.isPlaying
              const isSpace = /^\s+$/.test(word)
              const isPunctuation = /^[，。！？,.!?;:]+$/.test(word)

              if (isSpace) {
                return <span key={wordIndex}> </span>
              }

              const nextWord = words[wordIndex + 1]
              const needsSpace =
                nextWord &&
                !isPunctuation &&
                !/^\s+$/.test(nextWord) &&
                !/^[，。！？,.!?;:]+$/.test(nextWord)

              return (
                <React.Fragment key={wordIndex}>
                  <span className="relative inline">
                    {/* 单词背景高亮 */}
                    {isCurrentWord && (
                      <span
                        className={`absolute inset-0 -inset-x-0.5 -inset-y-0.5 rounded pointer-events-none z-10 ${
                          settings.theme === 'dark'
                            ? 'bg-blue-500 shadow-lg'
                            : settings.theme === 'sepia'
                              ? 'bg-blue-600 shadow-lg'
                              : 'bg-blue-600 shadow-lg'
                        }`}
                        style={{
                          animation: 'wordHighlight 0.15s ease-out',
                        }}
                      />
                    )}
                    <span
                      className={`relative z-20 ${
                        isCurrentWord
                          ? 'text-white font-medium'
                          : isCurrentSentence && readingState.isPlaying
                            ? settings.theme === 'dark'
                              ? 'text-yellow-100'
                              : settings.theme === 'sepia'
                                ? 'text-yellow-800'
                                : 'text-yellow-800'
                            : ''
                      }`}
                    >
                      {word}
                    </span>
                  </span>
                  {needsSpace && <span> </span>}
                </React.Fragment>
              )
            })}
            {sentenceIndex < sentences.length - 1 && (
              <span className="inline-block w-2"></span>
            )}
          </span>
        )
      })}
    </div>
  )
})

VirtualParagraph.displayName = 'VirtualParagraph'
