import { BookData } from '../pages/ReaderPage'

export interface BookMetadata {
  id: string
  title: string
  fileName: string
  fileSize: number
  uploadedAt: Date
  lastReadAt?: Date
  readingProgress: number // 0-100
  currentParagraph: number
  totalParagraphs: number
  bookmarks: BookmarkData[]
}

export interface BookmarkData {
  id: string
  paragraphIndex: number
  title: string
  createdAt: Date
  note?: string
}

class BookService {
  private readonly STORAGE_KEY = 'reading-platform-books'
  private readonly CONTENT_KEY_PREFIX = 'reading-platform-content-'

  // 获取所有书籍元数据
  public getBooks(): BookMetadata[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (!stored) {
        // 如果没有存储的书籍，创建一些示例数据
        const sampleBooks = this.createSampleBooks()
        this.saveBooksMetadata(sampleBooks)
        return sampleBooks
      }

      const books = JSON.parse(stored) as BookMetadata[]
      // 转换日期字符串为Date对象
      return books.map(book => ({
        ...book,
        uploadedAt: new Date(book.uploadedAt),
        lastReadAt: book.lastReadAt ? new Date(book.lastReadAt) : undefined,
        bookmarks: book.bookmarks.map(bookmark => ({
          ...bookmark,
          createdAt: new Date(bookmark.createdAt),
        })),
      }))
    } catch (error) {
      console.error('Error loading books:', error)
      return []
    }
  }

  // 获取单本书的完整内容
  public getBookContent(bookId: string): string | null {
    try {
      return localStorage.getItem(this.CONTENT_KEY_PREFIX + bookId)
    } catch (error) {
      console.error('Error loading book content:', error)
      return null
    }
  }

  // 获取完整的书籍数据
  public getBook(bookId: string): BookData | null {
    const books = this.getBooks()
    const metadata = books.find(book => book.id === bookId)
    if (!metadata) return null

    const content = this.getBookContent(bookId)
    if (!content) return null

    return {
      id: metadata.id,
      title: metadata.title,
      content,
      fileName: metadata.fileName,
      uploadedAt: metadata.uploadedAt,
    }
  }

  // 保存新书籍
  public saveBook(book: BookData): BookMetadata {
    const books = this.getBooks()

    // 计算段落数量
    const paragraphs = book.content
      .split(/\n\s*\n/)
      .filter(p => p.trim().length > 0)

    const metadata: BookMetadata = {
      id: book.id,
      title: book.title,
      fileName: book.fileName,
      fileSize: new Blob([book.content]).size,
      uploadedAt: book.uploadedAt,
      readingProgress: 0,
      currentParagraph: 0,
      totalParagraphs: paragraphs.length,
      bookmarks: [],
    }

    // 保存元数据
    const updatedBooks = books.filter(b => b.id !== book.id) // 移除重复
    updatedBooks.push(metadata)
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(updatedBooks))

    // 保存内容
    localStorage.setItem(this.CONTENT_KEY_PREFIX + book.id, book.content)

    return metadata
  }

  // 更新阅读进度
  public updateReadingProgress(
    bookId: string,
    progress: {
      currentParagraph: number
      readingProgress: number
    }
  ): void {
    const books = this.getBooks()
    const bookIndex = books.findIndex(book => book.id === bookId)

    if (bookIndex !== -1) {
      books[bookIndex] = {
        ...books[bookIndex],
        ...progress,
        lastReadAt: new Date(),
      }
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(books))
    }
  }

  // 添加书签
  public addBookmark(
    bookId: string,
    bookmark: Omit<BookmarkData, 'id' | 'createdAt'>
  ): BookmarkData {
    const books = this.getBooks()
    const bookIndex = books.findIndex(book => book.id === bookId)

    if (bookIndex === -1) {
      throw new Error('Book not found')
    }

    const newBookmark: BookmarkData = {
      ...bookmark,
      id: Date.now().toString(36) + Math.random().toString(36).substr(2),
      createdAt: new Date(),
    }

    books[bookIndex].bookmarks.push(newBookmark)
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(books))

    return newBookmark
  }

  // 删除书签
  public removeBookmark(bookId: string, bookmarkId: string): void {
    const books = this.getBooks()
    const bookIndex = books.findIndex(book => book.id === bookId)

    if (bookIndex !== -1) {
      books[bookIndex].bookmarks = books[bookIndex].bookmarks.filter(
        bookmark => bookmark.id !== bookmarkId
      )
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(books))
    }
  }

  // 删除书籍
  public deleteBook(bookId: string): void {
    const books = this.getBooks()
    const updatedBooks = books.filter(book => book.id !== bookId)

    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(updatedBooks))
    localStorage.removeItem(this.CONTENT_KEY_PREFIX + bookId)
  }

  // 搜索书籍
  public searchBooks(query: string): BookMetadata[] {
    const books = this.getBooks()
    const lowercaseQuery = query.toLowerCase()

    return books.filter(
      book =>
        book.title.toLowerCase().includes(lowercaseQuery) ||
        book.fileName.toLowerCase().includes(lowercaseQuery)
    )
  }

  // 获取最近阅读的书籍
  public getRecentBooks(limit: number = 5): BookMetadata[] {
    const books = this.getBooks()
    return books
      .filter(book => book.lastReadAt)
      .sort((a, b) => b.lastReadAt!.getTime() - a.lastReadAt!.getTime())
      .slice(0, limit)
  }

  // 获取存储使用情况
  public getStorageInfo(): {
    totalBooks: number
    totalSize: number
    usedStorage: number
    availableStorage: number
  } {
    const books = this.getBooks()
    const totalSize = books.reduce((sum, book) => sum + book.fileSize, 0)

    // 估算已使用的localStorage空间
    let usedStorage = 0
    try {
      const allKeys = Object.keys(localStorage)
      for (const key of allKeys) {
        if (key.startsWith('reading-platform-')) {
          usedStorage += localStorage.getItem(key)?.length || 0
        }
      }
    } catch (error) {
      console.error('Error calculating storage usage:', error)
    }

    // localStorage通常限制为5-10MB，这里假设5MB
    const maxStorage = 5 * 1024 * 1024 // 5MB in bytes

    return {
      totalBooks: books.length,
      totalSize,
      usedStorage,
      availableStorage: Math.max(0, maxStorage - usedStorage),
    }
  }

  // 创建示例书籍数据
  private createSampleBooks(): BookMetadata[] {
    const sampleContent = `第一章 开始

这是一个关于阅读平台的示例文本。这个平台支持语音朗读功能，可以将文本转换为语音。

用户可以选择不同的语音选项，包括Microsoft Edge TTS提供的高质量语音，以及浏览器内置的语音。

Chapter 2 Features

This is an English paragraph to test word spacing. The reading platform supports text-to-speech functionality with multiple voice options.

Users can choose from different voice providers including Microsoft Edge TTS for high-quality cloud-based synthesis and browser built-in voices for quick local response.

第三章 使用方法

点击页面顶部的语音按钮可以显示或隐藏语音选择器。

Mixed content: This paragraph contains both English words and 中文字符 to test the spacing between different types of text elements.

选择不同的语音后，系统会自动切换到新的语音进行朗读。

这样用户就可以根据自己的喜好选择最适合的语音了。`

    const sampleBook: BookMetadata = {
      id: 'sample-book-1',
      title: '阅读平台使用指南',
      fileName: 'reading-guide.txt',
      fileSize: sampleContent.length,
      uploadedAt: new Date(),
      readingProgress: 0,
      currentParagraph: 0,
      totalParagraphs: sampleContent.split(/\n\s*\n/).length,
      bookmarks: [],
    }

    // 保存示例书籍内容
    localStorage.setItem(this.CONTENT_KEY_PREFIX + sampleBook.id, sampleContent)

    return [sampleBook]
  }

  // 保存书籍元数据
  private saveBooksMetadata(books: BookMetadata[]) {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(books))
    } catch (error) {
      console.error('Error saving books metadata:', error)
    }
  }

  // 清理所有数据
  public clearAllData(): void {
    const allKeys = Object.keys(localStorage)
    for (const key of allKeys) {
      if (key.startsWith('reading-platform-')) {
        localStorage.removeItem(key)
      }
    }
  }
}

// 单例实例
export const bookService = new BookService()
