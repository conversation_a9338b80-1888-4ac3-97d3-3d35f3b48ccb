import { VoiceInfo } from '../../api/tts'
import {
  SpeechOptions,
  SpeechEvent,
  SpeechEventCallback,
} from './SpeechService'

export interface TTSVoice {
  id: string
  name: string
  language: string
  gender: string
  provider: 'browser' | 'edge-tts'
  nativeVoice?: SpeechSynthesisVoice
  edgeVoiceId?: string
}

export interface LanguageDetectionResult {
  language: string
  confidence: number
}

export class EnhancedTTSService {
  private synthesis: SpeechSynthesis
  private currentUtterance: SpeechSynthesisUtterance | null = null
  private currentAudio: HTMLAudioElement | null = null
  private isInitialized = false
  private eventCallbacks: SpeechEventCallback[] = []
  private currentText = ''
  private currentOptions: SpeechOptions = {
    rate: 1.0,
    pitch: 1.0,
    volume: 1.0,
    lang: 'en-US',
    provider: 'browser',
  }
  private edgeVoices: VoiceInfo[] = []
  private browserVoices: SpeechSynthesisVoice[] = []

  // 自适应同步校准系统
  private syncCalibration = {
    baseOffset: -30,
    adaptiveOffset: 0,
    calibrationHistory: [] as number[],
    lastCalibrationTime: 0,
    isCalibrating: false,
  }

  constructor() {
    this.synthesis = window.speechSynthesis
    this.initialize()
  }

  private async initialize() {
    // 初始化浏览器语音
    await this.initializeBrowserVoices()

    // 初始化 Edge TTS 语音
    await this.initializeEdgeVoices()

    this.isInitialized = true
  }

  private async initializeBrowserVoices() {
    if (this.synthesis.getVoices().length === 0) {
      await new Promise<void>(resolve => {
        const checkVoices = () => {
          if (this.synthesis.getVoices().length > 0) {
            resolve()
          } else {
            setTimeout(checkVoices, 100)
          }
        }
        this.synthesis.onvoiceschanged = checkVoices
        checkVoices()
      })
    }
    this.browserVoices = this.synthesis.getVoices()
  }

  private async initializeEdgeVoices() {
    try {
      // 动态导入 TTS API
      const { fetchVoices } = await import('../../api/tts')
      this.edgeVoices = await fetchVoices()
      console.log('Loaded Edge TTS voices:', this.edgeVoices.length)
    } catch (error) {
      console.warn(
        'Edge TTS API not available, using mock voices for demo:',
        error
      )
      // 提供一些常用的 Edge TTS 语音作为演示
      this.edgeVoices = [
        {
          id: 'zh-CN-XiaoxiaoNeural',
          name: 'Xiaoxiao (Neural)',
          language: 'zh-CN',
          gender: 'Female',
        },
        {
          id: 'zh-CN-YunyangNeural',
          name: 'Yunyang (Neural)',
          language: 'zh-CN',
          gender: 'Male',
        },
        {
          id: 'en-US-AriaNeural',
          name: 'Aria (Neural)',
          language: 'en-US',
          gender: 'Female',
        },
        {
          id: 'en-US-DavisNeural',
          name: 'Davis (Neural)',
          language: 'en-US',
          gender: 'Male',
        },
        {
          id: 'en-GB-SoniaNeural',
          name: 'Sonia (Neural)',
          language: 'en-GB',
          gender: 'Female',
        },
        {
          id: 'ja-JP-NanamiNeural',
          name: 'Nanami (Neural)',
          language: 'ja-JP',
          gender: 'Female',
        },
        {
          id: 'ko-KR-SunHiNeural',
          name: 'SunHi (Neural)',
          language: 'ko-KR',
          gender: 'Female',
        },
        {
          id: 'fr-FR-DeniseNeural',
          name: 'Denise (Neural)',
          language: 'fr-FR',
          gender: 'Female',
        },
        {
          id: 'de-DE-KatjaNeural',
          name: 'Katja (Neural)',
          language: 'de-DE',
          gender: 'Female',
        },
        {
          id: 'es-ES-ElviraNeural',
          name: 'Elvira (Neural)',
          language: 'es-ES',
          gender: 'Female',
        },
      ]
    }
  }

  // 获取所有可用语音
  public async getAllVoices(): Promise<TTSVoice[]> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    const voices: TTSVoice[] = []

    // 添加浏览器语音
    this.browserVoices.forEach(voice => {
      voices.push({
        id: `browser-${voice.name}`,
        name: voice.name,
        language: voice.lang,
        gender: this.inferGender(voice.name),
        provider: 'browser',
        nativeVoice: voice,
      })
    })

    // 添加 Edge TTS 语音
    this.edgeVoices.forEach(voice => {
      voices.push({
        id: `edge-${voice.id}`,
        name: voice.name,
        language: voice.language,
        gender: voice.gender,
        provider: 'edge-tts',
        edgeVoiceId: voice.id,
      })
    })

    return voices
  }

  // 根据语言获取推荐语音
  public async getRecommendedVoice(language: string): Promise<TTSVoice | null> {
    const voices = await this.getAllVoices()

    // 语言映射规则
    const languageMap: Record<string, string[]> = {
      zh: ['zh-CN', 'zh-TW', 'zh-HK'],
      en: ['en-US', 'en-GB', 'en-AU'],
      ja: ['ja-JP'],
      ko: ['ko-KR'],
      fr: ['fr-FR', 'fr-CA'],
      de: ['de-DE'],
      es: ['es-ES', 'es-MX'],
      ru: ['ru-RU'],
      it: ['it-IT'],
      pt: ['pt-BR', 'pt-PT'],
    }

    const targetLanguages = languageMap[language] || [language]

    // 优先选择 Edge TTS 语音（质量更好）
    for (const targetLang of targetLanguages) {
      const edgeVoice = voices.find(
        v => v.provider === 'edge-tts' && v.language.startsWith(targetLang)
      )
      if (edgeVoice) return edgeVoice
    }

    // 备选浏览器语音
    for (const targetLang of targetLanguages) {
      const browserVoice = voices.find(
        v => v.provider === 'browser' && v.language.startsWith(targetLang)
      )
      if (browserVoice) return browserVoice
    }

    return null
  }

  // 智能检测文本语言
  public detectLanguage(text: string): LanguageDetectionResult {
    const sample = text.slice(0, 200).toLowerCase()

    // 中文检测
    if (/[\u4e00-\u9fff]/.test(sample)) {
      const chineseRatio =
        (sample.match(/[\u4e00-\u9fff]/g) || []).length / sample.length
      if (chineseRatio > 0.3) {
        return { language: 'zh', confidence: chineseRatio }
      }
    }

    // 日文检测
    if (/[\u3040-\u309f\u30a0-\u30ff]/.test(sample)) {
      const japaneseRatio =
        (sample.match(/[\u3040-\u309f\u30a0-\u30ff]/g) || []).length /
        sample.length
      if (japaneseRatio > 0.2) {
        return { language: 'ja', confidence: japaneseRatio }
      }
    }

    // 韩文检测
    if (/[\uac00-\ud7af]/.test(sample)) {
      const koreanRatio =
        (sample.match(/[\uac00-\ud7af]/g) || []).length / sample.length
      if (koreanRatio > 0.2) {
        return { language: 'ko', confidence: koreanRatio }
      }
    }

    // 俄文检测
    if (/[\u0400-\u04ff]/.test(sample)) {
      const russianRatio =
        (sample.match(/[\u0400-\u04ff]/g) || []).length / sample.length
      if (russianRatio > 0.2) {
        return { language: 'ru', confidence: russianRatio }
      }
    }

    // 其他欧洲语言的简单检测
    const commonWords = {
      fr: [
        'le',
        'de',
        'et',
        'à',
        'un',
        'il',
        'être',
        'et',
        'en',
        'avoir',
        'que',
        'pour',
      ],
      de: [
        'der',
        'die',
        'und',
        'in',
        'den',
        'von',
        'zu',
        'das',
        'mit',
        'sich',
        'des',
        'auf',
      ],
      es: [
        'el',
        'de',
        'que',
        'y',
        'a',
        'en',
        'un',
        'es',
        'se',
        'no',
        'te',
        'lo',
      ],
      it: [
        'il',
        'di',
        'che',
        'e',
        'la',
        'per',
        'un',
        'in',
        'con',
        'del',
        'da',
        'al',
      ],
      pt: [
        'o',
        'de',
        'e',
        'do',
        'da',
        'em',
        'um',
        'para',
        'é',
        'com',
        'não',
        'uma',
      ],
    }

    for (const [lang, words] of Object.entries(commonWords)) {
      const matches = words.filter(word => sample.includes(` ${word} `)).length
      if (matches >= 2) {
        return { language: lang, confidence: matches / words.length }
      }
    }

    // 默认英文
    return { language: 'en', confidence: 0.5 }
  }

  // 智能选择语音
  public async selectVoiceForText(text: string): Promise<TTSVoice | null> {
    const detection = this.detectLanguage(text)
    return await this.getRecommendedVoice(detection.language)
  }

  private inferGender(voiceName: string): string {
    const name = voiceName.toLowerCase()
    if (
      name.includes('female') ||
      name.includes('woman') ||
      name.includes('aria') ||
      name.includes('zira') ||
      name.includes('xiaoxiao') ||
      name.includes('xiaoyi')
    ) {
      return 'Female'
    }
    if (
      name.includes('male') ||
      name.includes('man') ||
      name.includes('david') ||
      name.includes('mark') ||
      name.includes('yunyang')
    ) {
      return 'Male'
    }
    return 'Unknown'
  }

  public addEventListener(callback: SpeechEventCallback) {
    this.eventCallbacks.push(callback)
  }

  public removeEventListener(callback: SpeechEventCallback) {
    const index = this.eventCallbacks.indexOf(callback)
    if (index > -1) {
      this.eventCallbacks.splice(index, 1)
    }
  }

  private emitEvent(event: SpeechEvent) {
    this.eventCallbacks.forEach(callback => callback(event))
  }

  public async speak(
    text: string,
    options: Partial<SpeechOptions> = {}
  ): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    this.stop()
    this.currentText = text
    this.currentOptions = { ...this.currentOptions, ...options }

    // 如果没有指定提供商，智能选择
    if (!this.currentOptions.provider) {
      const recommendedVoice = await this.selectVoiceForText(text)
      if (recommendedVoice) {
        this.currentOptions.provider = recommendedVoice.provider
        if (recommendedVoice.provider === 'edge-tts') {
          this.currentOptions.edgeVoiceId = recommendedVoice.edgeVoiceId
        } else {
          this.currentOptions.voice = recommendedVoice.nativeVoice
        }
      }
    }

    if (this.currentOptions.provider === 'edge-tts') {
      return this.speakWithEdgeTTS(text)
    } else {
      return this.speakWithBrowser(text)
    }
  }

  private async speakWithBrowser(text: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const utterance = new SpeechSynthesisUtterance(text)

      utterance.rate = this.currentOptions.rate
      utterance.pitch = this.currentOptions.pitch
      utterance.volume = this.currentOptions.volume

      if (this.currentOptions.voice) {
        utterance.voice = this.currentOptions.voice
      } else if (this.currentOptions.lang) {
        utterance.lang = this.currentOptions.lang
      }

      utterance.onstart = () => this.emitEvent({ type: 'start' })
      utterance.onend = () => {
        this.currentUtterance = null
        this.emitEvent({ type: 'end' })
        resolve()
      }
      utterance.onerror = event => {
        this.currentUtterance = null
        const error = `Speech synthesis error: ${event.error}`
        this.emitEvent({ type: 'error', error })
        reject(new Error(error))
      }
      utterance.onboundary = event => {
        this.emitEvent({
          type: 'boundary',
          charIndex: event.charIndex,
          charLength: event.charLength,
          elapsedTime: event.elapsedTime,
        })
      }

      this.currentUtterance = utterance
      this.synthesis.speak(utterance)
    })
  }

  private async speakWithEdgeTTS(text: string): Promise<void> {
    try {
      // 优先尝试带时间戳的接口
      const { synthesizeWithTimings, synthesizeStream } = await import(
        '../../api/tts'
      )
      let audio: HTMLAudioElement
      let timings: Array<{
        time_ms: number
        text_offset?: number
        text_length?: number
      }> | null = null
      let objectUrlToRevoke: string | null = null

      try {
        const res = await synthesizeWithTimings({
          text,
          voice: this.currentOptions.edgeVoiceId || 'en-US-AriaNeural',
          speed: this.currentOptions.rate,
          pitch: this.currentOptions.pitch,
          output_format: 'mp3',
        })
        audio = new Audio(`data:${res.mime};base64,${res.audio_base64}`)
        timings = (res.timings || [])
          .slice()
          .sort((a, b) => a.time_ms - b.time_ms)
      } catch (e) {
        // 回退到流式 blob（无时间戳）
        const blob = await synthesizeStream({
          text,
          voice: this.currentOptions.edgeVoiceId || 'en-US-AriaNeural',
          speed: this.currentOptions.rate,
          pitch: this.currentOptions.pitch,
          output_format: 'mp3',
        })
        objectUrlToRevoke = URL.createObjectURL(blob)
        audio = new Audio()
        audio.src = objectUrlToRevoke
      }

      audio.volume = this.currentOptions.volume

      // 预处理文本单词起始位置（用于没有时间戳或时间戳不含 text_offset 时）
      const textForBoundary = this.currentText || ''
      const wordStartIndices: number[] = []
      const wordRegex = /\S+/g
      let match: RegExpExecArray | null
      while ((match = wordRegex.exec(textForBoundary)) !== null) {
        wordStartIndices.push(match.index)
      }
      // 若只得到一个“词”（常见于中文/日文等无空格文本），则退化为逐字符边界
      if (wordStartIndices.length <= 1) {
        wordStartIndices.length = 0
        for (let i = 0; i < textForBoundary.length; i++) {
          const ch = textForBoundary[i]
          // 跳过空白
          if (ch.trim().length === 0) continue
          wordStartIndices.push(i)
        }
      }

      // 将 timing 映射为 charIndex 的辅助函数
      const charIndexForTiming = (i: number): number => {
        if (timings && timings[i]) {
          const t = timings[i]
          // 优先使用服务器返回的 text_offset
          if (typeof t.text_offset === 'number') return t.text_offset
        }
        // 回退到基于索引的估算
        if (wordStartIndices.length > 0) {
          return wordStartIndices[Math.min(i, wordStartIndices.length - 1)] || 0
        }
        return 0
      }

      let lastIdx = -1
      let lastEmitTime = 0
      const predictiveOffset = 0

      // 自适应校准系统
      const calibrateSync = (actualTime: number, expectedTime: number) => {
        const error = actualTime - expectedTime
        this.syncCalibration.calibrationHistory.push(error)

        // 保持最近20次的校准历史
        if (this.syncCalibration.calibrationHistory.length > 20) {
          this.syncCalibration.calibrationHistory.shift()
        }

        // 计算平均误差并调整偏移
        const avgError =
          this.syncCalibration.calibrationHistory.reduce((a, b) => a + b, 0) /
          this.syncCalibration.calibrationHistory.length

        // 渐进式调整，避免过度校正
        this.syncCalibration.adaptiveOffset = avgError * 0.3
      }

      // 动态计算偏移量，基于音频延迟和系统性能
      const calculateDynamicOffset = () => {
        if (!timings || timings.length === 0) return 0

        // 基础偏移量：补偿音频处理延迟
        let baseOffset =
          this.syncCalibration.baseOffset + this.syncCalibration.adaptiveOffset

        // 根据播放速度调整
        const speedFactor = this.currentOptions.rate || 1.0
        baseOffset = baseOffset / speedFactor

        // 根据系统性能调整（通过RAF间隔估算）
        const rafInterval = performance.now() - lastEmitTime
        if (rafInterval > 20) baseOffset -= 10 // 系统较慢，提前更多

        return Math.max(-150, Math.min(100, baseOffset))
      }

      // 预测性同步：提前计算下一个单词的时机
      const predictNextWordTiming = (currentIdx: number): number => {
        if (!timings || currentIdx >= timings.length - 1) return -1

        const currentTime = audio.currentTime * 1000
        const nextWordTime = timings[currentIdx + 1].time_ms
        const timeToNext = nextWordTime - currentTime

        // 如果下一个单词即将到来（100ms内），预先准备
        return timeToNext <= 100 ? currentIdx + 1 : -1
      }

      // 增强的边界事件发射器，支持预测性同步
      const emitBoundaryEvent = (
        idx: number,
        charIndex: number,
        charLength: number,
        elapsedTime: number,
        isPredictive: boolean = false
      ) => {
        const now = performance.now()

        // 预测性事件使用更短的防抖间隔
        const minInterval = isPredictive ? 5 : idx <= 3 ? 8 : 15

        if (
          idx === lastIdx &&
          now - lastEmitTime < minInterval &&
          !isPredictive
        )
          return

        lastIdx = idx
        lastEmitTime = now

        this.emitEvent({
          type: 'boundary',
          charIndex,
          charLength,
          elapsedTime: elapsedTime + (isPredictive ? predictiveOffset : 0),
          isPredictive, // 添加预测标志
        })
      }

      // 高精度同步系统：结合RAF和高频定时器
      let rafId: number | null = null
      let highFreqTimerId: number | null = null

      const upperBound = (arr: Array<{ time_ms: number }>, target: number) => {
        let lo = 0
        let hi = arr.length
        while (lo < hi) {
          const mid = (lo + hi) >> 1
          if (arr[mid].time_ms <= target) lo = mid + 1
          else hi = mid
        }
        return lo
      }

      // 高频率同步检查（每5ms一次，比RAF更频繁）
      const highFreqSync = () => {
        if (!audio.duration || audio.paused || audio.ended) return

        const dynamicOffset = calculateDynamicOffset()
        const nowMs = audio.currentTime * 1000 + dynamicOffset

        if (timings && timings.length > 0) {
          const pos = upperBound(timings, nowMs) - 1
          if (pos >= 0 && pos < timings.length && pos !== lastIdx) {
            // 高频检查发现的变化，立即更新
            calibrateSync(nowMs, timings[pos].time_ms)
            emitBoundaryEvent(
              pos,
              charIndexForTiming(pos),
              timings[pos].text_length ?? 0,
              nowMs
            )
          }
        }
      }

      const tick = () => {
        if (!audio.duration || audio.paused || audio.ended) {
          rafId = requestAnimationFrame(tick)
          return
        }

        // 动态计算偏移量
        const dynamicOffset = calculateDynamicOffset()
        const nowMs = audio.currentTime * 1000 + dynamicOffset
        let idx = -1

        if (timings && timings.length > 0) {
          // 特殊处理：如果音频刚开始播放，立即高亮第一个单词
          if (audio.currentTime < 0.3 && lastIdx === -1) {
            idx = 0
          } else {
            // 使用二分查找找到当前时间对应的单词索引
            const pos = upperBound(timings, nowMs) - 1
            if (pos >= 0 && pos < timings.length) {
              idx = pos
            }
          }

          // 预测性同步：检查是否需要预先高亮下一个单词
          const predictiveIdx = predictNextWordTiming(idx)
          if (predictiveIdx > idx && predictiveIdx < timings.length) {
            emitBoundaryEvent(
              predictiveIdx,
              charIndexForTiming(predictiveIdx),
              timings[predictiveIdx].text_length ?? 0,
              timings[predictiveIdx].time_ms,
              true // 预测性事件
            )
          }
        } else if (wordStartIndices.length > 0) {
          // 基于进度的估算
          const progress = audio.currentTime / (audio.duration || 1)
          idx = Math.min(
            wordStartIndices.length - 1,
            Math.floor(progress * wordStartIndices.length)
          )
        }

        if (idx >= 0) {
          // 执行自适应校准
          if (timings && timings[idx]) {
            calibrateSync(nowMs, timings[idx].time_ms)
          }

          emitBoundaryEvent(
            idx,
            charIndexForTiming(idx),
            timings && timings[idx] ? (timings[idx].text_length ?? 0) : 0,
            nowMs
          )
        }

        rafId = requestAnimationFrame(tick)
      }
      const startRaf = () => {
        if (rafId == null) rafId = requestAnimationFrame(tick)
      }
      const stopRaf = () => {
        if (rafId != null) {
          cancelAnimationFrame(rafId)
          rafId = null
        }
      }

      const startHighFreqSync = () => {
        if (highFreqTimerId == null) {
          highFreqTimerId = window.setInterval(highFreqSync, 5) // 每5ms检查一次
        }
      }
      const stopHighFreqSync = () => {
        if (highFreqTimerId != null) {
          clearInterval(highFreqTimerId)
          highFreqTimerId = null
        }
      }
      const onPlay = () => {
        // 重置状态
        lastIdx = -1
        lastEmitTime = 0

        // 重置校准系统
        this.syncCalibration.calibrationHistory = []
        this.syncCalibration.lastCalibrationTime = performance.now()

        if (timings && timings.length > 0) {
          // 启动双重同步系统：RAF + 高频定时器
          startRaf()
          startHighFreqSync()
        } else {
          // 没有timings时，只使用RAF
          startRaf()
        }
        this.emitEvent({ type: 'start' })
      }
      const onPause = () => {
        stopRaf()
        stopHighFreqSync()
      }

      // 添加更多音频事件监听器以获得最精确的同步
      audio.addEventListener('playing', onPlay)
      audio.addEventListener('play', onPlay)
      audio.addEventListener('loadedmetadata', () => {
        if (!audio.paused) onPlay()
      })
      audio.addEventListener('pause', onPause)
      audio.addEventListener('canplay', () => {
        // 音频准备就绪时立即开始同步
        if (!audio.paused) onPlay()
      })
      audio.addEventListener('timeupdate', () => {
        // 时间更新时检查同步状态
        if (!audio.paused && timings && timings.length > 0) {
          const dynamicOffset = calculateDynamicOffset()
          const currentTime = audio.currentTime * 1000 + dynamicOffset
          const pos = upperBound(timings, currentTime) - 1
          if (pos >= 0 && pos < timings.length && pos !== lastIdx) {
            emitBoundaryEvent(
              pos,
              charIndexForTiming(pos),
              timings[pos].text_length ?? 0,
              currentTime
            )
          }
        }
      })

      return new Promise((resolve, reject) => {
        audio.onplay = onPlay
        audio.onended = () => {
          stopRaf()
          stopHighFreqSync()

          // 确保最后一个词也发出边界事件
          if (timings && timings.length > 0) {
            const finalIdx = timings.length - 1
            if (finalIdx !== lastIdx && finalIdx >= 0) {
              this.emitEvent({
                type: 'boundary',
                charIndex: charIndexForTiming(finalIdx),
                charLength: timings[finalIdx].text_length ?? 0,
                elapsedTime: timings[finalIdx].time_ms,
              })
            }
          } else if (
            wordStartIndices.length > 0 &&
            lastIdx !== wordStartIndices.length - 1
          ) {
            const finalIdx = wordStartIndices.length - 1
            this.emitEvent({
              type: 'boundary',
              charIndex: wordStartIndices[finalIdx],
              charLength: 0,
              elapsedTime: (audio.duration || 0) * 1000,
            })
          }

          if (objectUrlToRevoke) URL.revokeObjectURL(objectUrlToRevoke)
          this.currentAudio = null
          this.emitEvent({ type: 'end' })
          resolve()
        }
        audio.onerror = () => {
          stopRaf()
          stopHighFreqSync()
          if (objectUrlToRevoke) URL.revokeObjectURL(objectUrlToRevoke)
          this.currentAudio = null
          const error = 'Edge TTS playback error'
          this.emitEvent({ type: 'error', error })
          reject(new Error(error))
        }

        this.currentAudio = audio
        audio.play().catch(reject)
      })
    } catch (error) {
      console.warn(
        'Edge TTS API not available, falling back to browser TTS:',
        error
      )
      // 回退到浏览器 TTS
      return this.speakWithBrowser(text)
    }
  }

  public pause() {
    if (this.currentOptions.provider === 'edge-tts' && this.currentAudio) {
      this.currentAudio.pause()
      this.emitEvent({ type: 'pause' })
    } else if (this.synthesis.speaking && !this.synthesis.paused) {
      this.synthesis.pause()
      this.emitEvent({ type: 'pause' })
    }
  }

  public resume() {
    if (this.currentOptions.provider === 'edge-tts' && this.currentAudio) {
      this.currentAudio.play()
      this.emitEvent({ type: 'resume' })
    } else if (this.synthesis.paused) {
      this.synthesis.resume()
      this.emitEvent({ type: 'resume' })
    }
  }

  public stop() {
    if (this.currentAudio) {
      this.currentAudio.pause()
      this.currentAudio = null
    }
    if (this.synthesis.speaking || this.synthesis.pending) {
      this.synthesis.cancel()
      this.currentUtterance = null
    }
  }

  public isPlaying(): boolean {
    if (this.currentOptions.provider === 'edge-tts') {
      return this.currentAudio ? !this.currentAudio.paused : false
    }
    return this.synthesis.speaking && !this.synthesis.paused
  }

  public isPaused(): boolean {
    if (this.currentOptions.provider === 'edge-tts') {
      return this.currentAudio ? this.currentAudio.paused : false
    }
    return this.synthesis.paused
  }

  public updateOptions(options: Partial<SpeechOptions>) {
    this.currentOptions = { ...this.currentOptions, ...options }
  }

  public getCurrentOptions(): SpeechOptions {
    return { ...this.currentOptions }
  }

  // 配置同步参数以实现完美同步
  public configurePerfectSync(options: {
    baseOffset?: number // 基础偏移量（毫秒）
    enableHighFreqSync?: boolean // 是否启用高频同步
    calibrationSensitivity?: number // 校准敏感度 (0-1)
  }) {
    if (options.baseOffset !== undefined) {
      this.syncCalibration.baseOffset = options.baseOffset
    }
    if (options.calibrationSensitivity !== undefined) {
      // 校准敏感度影响自适应调整的强度
      this.syncCalibration.adaptiveOffset *= options.calibrationSensitivity
    }
  }

  // 获取当前同步状态
  public getSyncStatus() {
    return {
      baseOffset: this.syncCalibration.baseOffset,
      adaptiveOffset: this.syncCalibration.adaptiveOffset,
      calibrationHistory: [...this.syncCalibration.calibrationHistory],
      avgError:
        this.syncCalibration.calibrationHistory.length > 0
          ? this.syncCalibration.calibrationHistory.reduce((a, b) => a + b, 0) /
            this.syncCalibration.calibrationHistory.length
          : 0,
    }
  }

  // 重置同步校准
  public resetSyncCalibration() {
    this.syncCalibration.calibrationHistory = []
    this.syncCalibration.adaptiveOffset = 0
    this.syncCalibration.lastCalibrationTime = 0
  }

  public destroy() {
    this.stop()
    this.eventCallbacks = []
  }
}

// 单例实例
export const enhancedTTSService = new EnhancedTTSService()
