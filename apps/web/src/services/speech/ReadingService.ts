import { enhancedTTSService, TTSVoice } from './EnhancedTTSService'
import { SpeechOptions } from './SpeechService'

export interface ReadingPosition {
  paragraphIndex: number
  sentenceIndex: number
  wordIndex: number
}

export interface ReadingProgress {
  position: ReadingPosition
  totalParagraphs: number
  totalSentences: number
  totalWords: number
  elapsedTime: number
  currentVoice?: TTSVoice
}

export type ReadingEventCallback = (progress: ReadingProgress) => void

export class ReadingService {
  private paragraphs: string[] = []
  private sentences: string[][] = []
  private words: string[][][] = []
  private currentPosition: ReadingPosition = {
    paragraphIndex: 0,
    sentenceIndex: 0,
    wordIndex: 0,
  }
  private isReading = false
  private isPaused = false
  private startTime = 0
  private pausedTime = 0
  private eventCallbacks: ReadingEventCallback[] = []
  private readingOptions: SpeechOptions = {
    rate: 1.0,
    pitch: 1.0,
    volume: 1.0,
    lang: 'en-US',
    provider: 'browser',
  }
  private currentVoice: TTSVoice | null = null

  constructor() {
    this.setupSpeechEventListeners()
  }

  private setupSpeechEventListeners() {
    enhancedTTSService.addEventListener(event => {
      switch (event.type) {
        case 'start':
          this.isReading = true
          this.isPaused = false
          if (this.startTime === 0) {
            this.startTime = Date.now()
          }
          break

        case 'end':
          this.handleSentenceEnd()
          break

        case 'pause':
          this.isPaused = true
          this.pausedTime = Date.now()
          break

        case 'resume':
          this.isPaused = false
          if (this.pausedTime > 0) {
            this.startTime += Date.now() - this.pausedTime
            this.pausedTime = 0
          }
          break

        case 'boundary':
          if (event.charIndex !== undefined) {
            this.handleWordBoundary(event.charIndex)
          }
          break

        case 'error':
          this.isReading = false
          this.isPaused = false
          console.error('Reading error:', event.error)
          break
      }

      this.emitProgress()
    })
  }

  private handleWordBoundary(charIndex: number) {
    const currentSentence = this.getCurrentSentence()
    if (!currentSentence) return

    // 更精确的单词索引计算
    // 使用正则表达式匹配单词，与后端保持一致
    const words = []
    const wordRegex = /\S+/g
    let match
    while ((match = wordRegex.exec(currentSentence)) !== null) {
      words.push({
        word: match[0],
        start: match.index,
        end: match.index + match[0].length,
      })
    }

    // 找到charIndex对应的单词
    let wordIndex = 0
    for (let i = 0; i < words.length; i++) {
      if (charIndex >= words[i].start && charIndex < words[i].end) {
        wordIndex = i
        break
      } else if (charIndex >= words[i].start) {
        wordIndex = i
      }
    }

    // 确保索引在有效范围内
    const { paragraphIndex, sentenceIndex } = this.currentPosition
    if (
      this.words[paragraphIndex] &&
      this.words[paragraphIndex][sentenceIndex]
    ) {
      const maxWordIndex = this.words[paragraphIndex][sentenceIndex].length - 1
      this.currentPosition.wordIndex = Math.min(wordIndex, maxWordIndex)
    } else {
      this.currentPosition.wordIndex = 0
    }
  }

  private handleSentenceEnd() {
    const { paragraphIndex, sentenceIndex } = this.currentPosition

    // 安全检查：确保数组存在
    if (!this.sentences || !this.sentences[paragraphIndex]) {
      console.warn('ReadingService: sentences array not properly initialized')
      this.isReading = false
      this.isPaused = false
      return
    }

    // 移动到下一个句子
    if (sentenceIndex < this.sentences[paragraphIndex].length - 1) {
      this.currentPosition.sentenceIndex++
      this.currentPosition.wordIndex = 0
      this.readCurrentSentence()
    } else {
      // 移动到下一个段落
      if (paragraphIndex < this.paragraphs.length - 1) {
        this.currentPosition.paragraphIndex++
        this.currentPosition.sentenceIndex = 0
        this.currentPosition.wordIndex = 0
        this.readCurrentSentence()
      } else {
        // 阅读完成
        this.isReading = false
        this.isPaused = false
      }
    }
  }

  private getCurrentSentence(): string | null {
    const { paragraphIndex, sentenceIndex } = this.currentPosition
    if (
      paragraphIndex >= 0 &&
      paragraphIndex < this.sentences.length &&
      sentenceIndex >= 0 &&
      sentenceIndex < this.sentences[paragraphIndex].length
    ) {
      return this.sentences[paragraphIndex][sentenceIndex]
    }
    return null
  }

  private async readCurrentSentence() {
    const sentence = this.getCurrentSentence()
    if (sentence && this.isReading) {
      // 智能选择语音（如果还没有选择）
      if (!this.currentVoice) {
        this.currentVoice =
          await enhancedTTSService.selectVoiceForText(sentence)
        if (this.currentVoice) {
          this.readingOptions.provider = this.currentVoice.provider
          if (this.currentVoice.provider === 'edge-tts') {
            this.readingOptions.edgeVoiceId = this.currentVoice.edgeVoiceId
          } else {
            this.readingOptions.voice = this.currentVoice.nativeVoice
          }
        }
      }

      await enhancedTTSService.speak(sentence, this.readingOptions)
    }
  }

  private parseText(text: string): void {
    // 分割段落
    this.paragraphs = text
      .split(/\n\s*\n/)
      .map(p => p.trim())
      .filter(p => p.length > 0)

    // 分割句子
    this.sentences = this.paragraphs.map(paragraph => {
      return paragraph
        .split(/[.!?。！？]+/)
        .map(s => s.trim())
        .filter(s => s.length > 0)
    })

    // 分割单词
    this.words = this.sentences.map(paragraphSentences => {
      return paragraphSentences.map(sentence => {
        return sentence.split(/\s+/).filter(w => w.length > 0)
      })
    })
  }

  private isInitialized(): boolean {
    return (
      this.paragraphs.length > 0 &&
      this.sentences.length > 0 &&
      this.words.length > 0 &&
      this.sentences.length === this.paragraphs.length &&
      this.words.length === this.paragraphs.length
    )
  }

  private emitProgress() {
    const progress: ReadingProgress = {
      position: { ...this.currentPosition },
      totalParagraphs: this.paragraphs.length,
      totalSentences: this.sentences.reduce(
        (total, sentences) => total + sentences.length,
        0
      ),
      totalWords: this.words.reduce(
        (total, paragraphWords) =>
          total +
          paragraphWords.reduce(
            (sentenceTotal, words) => sentenceTotal + words.length,
            0
          ),
        0
      ),
      elapsedTime: this.startTime > 0 ? Date.now() - this.startTime : 0,
      currentVoice: this.currentVoice || undefined,
    }

    this.eventCallbacks.forEach(callback => callback(progress))
  }

  public loadText(text: string) {
    this.stop()
    this.parseText(text)
    this.currentPosition = { paragraphIndex: 0, sentenceIndex: 0, wordIndex: 0 }
    this.startTime = 0
    this.pausedTime = 0
    this.currentVoice = null // 重置语音选择，让系统重新智能选择
  }

  public start() {
    if (this.paragraphs.length === 0) return

    if (!this.isInitialized()) {
      console.error(
        'ReadingService: Cannot start reading - service not properly initialized'
      )
      return
    }

    if (this.isPaused) {
      enhancedTTSService.resume()
    } else {
      this.isReading = true
      this.readCurrentSentence()
    }
  }

  public pause() {
    if (this.isReading && !this.isPaused) {
      enhancedTTSService.pause()
    }
  }

  public stop() {
    enhancedTTSService.stop()
    this.isReading = false
    this.isPaused = false
    this.currentPosition = { paragraphIndex: 0, sentenceIndex: 0, wordIndex: 0 }
    this.startTime = 0
    this.pausedTime = 0
  }

  public jumpTo(position: Partial<ReadingPosition>) {
    const newPosition = { ...this.currentPosition, ...position }

    // 验证位置有效性
    if (
      newPosition.paragraphIndex >= 0 &&
      newPosition.paragraphIndex < this.paragraphs.length &&
      this.sentences[newPosition.paragraphIndex] // 确保句子数组存在
    ) {
      const maxSentenceIndex =
        this.sentences[newPosition.paragraphIndex].length - 1
      newPosition.sentenceIndex = Math.max(
        0,
        Math.min(newPosition.sentenceIndex, maxSentenceIndex)
      )

      // 确保单词数组存在
      if (
        this.words[newPosition.paragraphIndex] &&
        this.words[newPosition.paragraphIndex][newPosition.sentenceIndex]
      ) {
        const maxWordIndex =
          this.words[newPosition.paragraphIndex][newPosition.sentenceIndex]
            .length - 1
        newPosition.wordIndex = Math.max(
          0,
          Math.min(newPosition.wordIndex, maxWordIndex)
        )
      } else {
        newPosition.wordIndex = 0
      }

      this.currentPosition = newPosition

      if (this.isReading) {
        enhancedTTSService.stop()
        this.readCurrentSentence()
      }
    }
  }

  public updateOptions(options: Partial<SpeechOptions>) {
    this.readingOptions = { ...this.readingOptions, ...options }
    enhancedTTSService.updateOptions(this.readingOptions)
  }

  public addEventListener(callback: ReadingEventCallback) {
    this.eventCallbacks.push(callback)
  }

  public removeEventListener(callback: ReadingEventCallback) {
    const index = this.eventCallbacks.indexOf(callback)
    if (index > -1) {
      this.eventCallbacks.splice(index, 1)
    }
  }

  public getIsReading(): boolean {
    return this.isReading
  }

  public getIsPaused(): boolean {
    return this.isPaused
  }

  public getCurrentVoice(): TTSVoice | null {
    return this.currentVoice
  }

  public async getAllVoices(): Promise<TTSVoice[]> {
    return await enhancedTTSService.getAllVoices()
  }

  public async setVoice(voice: TTSVoice) {
    this.currentVoice = voice
    this.readingOptions.provider = voice.provider
    if (voice.provider === 'edge-tts') {
      this.readingOptions.edgeVoiceId = voice.edgeVoiceId
    } else {
      this.readingOptions.voice = voice.nativeVoice
    }
    enhancedTTSService.updateOptions(this.readingOptions)
  }

  public getCurrentPosition(): ReadingPosition {
    return { ...this.currentPosition }
  }

  public getParagraphs(): string[] {
    return [...this.paragraphs]
  }

  public getSentences(): string[][] {
    return this.sentences.map(sentences => [...sentences])
  }

  public getWords(): string[][][] {
    return this.words.map(paragraphWords =>
      paragraphWords.map(words => [...words])
    )
  }

  public destroy() {
    this.stop()
    this.eventCallbacks = []
  }
}

// 单例实例
export const readingService = new ReadingService()
